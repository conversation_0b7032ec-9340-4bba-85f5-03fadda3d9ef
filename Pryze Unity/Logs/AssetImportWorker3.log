Unity Editor version:    6000.0.23f1 (1c4764c07fb4)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
-logFile
Logs/AssetImportWorker3.log
-srvPort
53700
-job-worker-count
4
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8782536448]  Target information:

Player connection [8782536448]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2522782634 [EditorId] 2522782634 [Version] 1048832 [Id] OSXEditor(0,MacBooks-MacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8782536448] Host joined multi-casting on [***********:54997]...
Player connection [8782536448] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 4
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 18.89 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.23f1 (1c4764c07fb4)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 Pro (high power)
Metal devices available: 1
0: Apple M1 Pro (high power)
Using device Apple M1 Pro (high power)
Initializing Metal device caps: Apple M1 Pro
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56728
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.001188 seconds.
- Loaded All Assemblies, in  0.338 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 214 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.739 seconds
Domain Reload Profiling: 1077ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (144ms)
		LoadAssemblies (113ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (141ms)
			TypeCache.Refresh (140ms)
				TypeCache.ScanAssembly (126ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (740ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (686ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (361ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (5ms)
			ProcessInitializeOnLoadAttributes (197ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  1.385 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.87 ms, found 5 plugins.
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.43 ms, found 5 plugins.
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.113 seconds
Domain Reload Profiling: 2497ms
	BeginReloadAssembly (607ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (713ms)
		LoadAssemblies (413ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (344ms)
			TypeCache.Refresh (257ms)
				TypeCache.ScanAssembly (231ms)
			BuildScriptInfoCaches (63ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1113ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (897ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (709ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.06 seconds
Refreshing native plugins compatible for Editor in 2.61 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 370 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9697 unused Assets / (6.3 MB). Loaded Objects now: 10440.
Memory consumption went from 232.9 MB to 226.6 MB.
Total: 11.513167 ms (FindLiveObjects: 1.862791 ms CreateObjectMapping: 0.313875 ms MarkObjects: 6.326375 ms  DeleteObjects: 3.009334 ms)

========================================================================
Received Import Request.
  Time since last request: 213226.898904 seconds.
  path: Assets/Games/Pool/Sprites/DontEdit/progress_circle.png
  artifactKey: Guid(2a079e1fe7cd44662a5d8f504eddd708) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Pool/Sprites/DontEdit/progress_circle.png using Guid(2a079e1fe7cd44662a5d8f504eddd708) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1913e204c8aa7e5e65630fec071e20cb') in 0.295471083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/UI/TutorialUI/pryzeCoins.png
  artifactKey: Guid(59db810523613164088e64953c7741cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/TutorialUI/pryzeCoins.png using Guid(59db810523613164088e64953c7741cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ae18dd7ad6833d82f9674e128d6d433') in 0.009335792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/UI/TutorialUI/Rectangle 2.png
  artifactKey: Guid(5360aa76b58d2634f85591df6f209d27) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/TutorialUI/Rectangle 2.png using Guid(5360aa76b58d2634f85591df6f209d27) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd685ff3c54925a1eb2a26ab62b2a9dc6') in 0.006475166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/UI/Rectangle 78.png
  artifactKey: Guid(a662761d17d850e4d8ddad960fdff882) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Rectangle 78.png using Guid(a662761d17d850e4d8ddad960fdff882) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0803a43d4a95bed7ed81bee044391783') in 0.008559417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/UI/TutorialUI/Rectangle 22.png
  artifactKey: Guid(cef1474c90abe2c47a12b61ea7793841) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/TutorialUI/Rectangle 22.png using Guid(cef1474c90abe2c47a12b61ea7793841) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '537ade9215bdedd6caa7ad9a3b1a1eee') in 0.007493542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/UI/splash screen/Pryze Logo.png
  artifactKey: Guid(8d1792be90ce8734da1ca4d09364dd63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/splash screen/Pryze Logo.png using Guid(8d1792be90ce8734da1ca4d09364dd63) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7063a6e21e3a8e6ad449ed73abb8c9e1') in 0.009667125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/UI/Sprites/Rectangle 1.png
  artifactKey: Guid(79d4ae6a389d11e449ecb941c0d8161a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/Rectangle 1.png using Guid(79d4ae6a389d11e449ecb941c0d8161a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '676aaa81b62de6a46a983cd9cce42a44') in 0.00972925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/UI/Demo Screens/Icons/UserProfileIcons/question-mark-circle.svg
  artifactKey: Guid(4913fc46931fb964cad3939a026c3c2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Demo Screens/Icons/UserProfileIcons/question-mark-circle.svg using Guid(4913fc46931fb964cad3939a026c3c2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d6f231d71fbdec2ce7e70d57749b6ec') in 0.353898125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000140 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Billie Jean” by Michael Jackson.png
  artifactKey: Guid(c90785bb05cee914492e08db8b5cbadc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Billie Jean” by Michael Jackson.png using Guid(c90785bb05cee914492e08db8b5cbadc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cd961926a25360cc22cb7a0b9405caeb') in 0.009433375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Baby One More Time” by Britney Spears.png
  artifactKey: Guid(451b614b13e598b45ab8c4b6e499be4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Baby One More Time” by Britney Spears.png using Guid(451b614b13e598b45ab8c4b6e499be4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9b4710c12fa338ddf895c1a83252723') in 0.010571916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Uptown Funk” by Mark Ronson and Bruno Mars.png
  artifactKey: Guid(28dfc0b06f0727e45a2fe803a094559e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Uptown Funk” by Mark Ronson and Bruno Mars.png using Guid(28dfc0b06f0727e45a2fe803a094559e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23fc2c60c1a937b7e756aff3c9a3f532') in 0.007568042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/UI/Demo Screens/Icons/WhatsappIcon.svg
  artifactKey: Guid(ccd370af878046a4da3fd186cef38319) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Demo Screens/Icons/WhatsappIcon.svg using Guid(ccd370af878046a4da3fd186cef38319) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dd1396651cb00ba266166ce8b8e8ca71') in 0.008739792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Thriller” by Michael Jackson.png
  artifactKey: Guid(e524a3b68f0e0074d8bc0405fe6f4bc5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Thriller” by Michael Jackson.png using Guid(e524a3b68f0e0074d8bc0405fe6f4bc5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db4056fab2c8cd6c30cf3c1d9c5e04fb') in 0.009972083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Don’t Stop Believing” by Journey.png
  artifactKey: Guid(3621269e3324615448725201e33a4936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Don’t Stop Believing” by Journey.png using Guid(3621269e3324615448725201e33a4936) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c6c07bf14846f7ce0f103e6938071c6f') in 0.009002167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
