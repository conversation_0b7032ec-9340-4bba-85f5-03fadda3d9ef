Base path: '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents', plugins path '/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=3946 file=Assets/DefaultResourcesExtra/UI/UI-Default.shader name=UI/Default pass=Default ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING uKW=UNITY_UI_CLIP_RECT dKW=UNITY_UI_ALPHACLIP UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435488 lang=0 type=Fragment platform=metal reqs=1 mask=6 start=49 ok=1 outsize=1793

Cmd: compileSnippet
  insize=6301 file=Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile.shader name=TextMeshPro/Mobile/Distance Field pass=<Unnamed Pass 0> ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING uKW=OUTLINE_ON UNDERLAY_ON UNITY_UI_CLIP_RECT dKW=UNDERLAY_INNER UNITY_UI_ALPHACLIP UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435488 lang=0 type=Fragment platform=metal reqs=33 mask=6 start=83 ok=1 outsize=2547

Cmd: compileSnippet
  insize=7825 file=Assets/TextMesh Pro/Shaders/TMP_SDF.shader name=TextMeshPro/Distance Field pass=<Unnamed Pass 0> ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING uKW=UNDERLAY_ON dKW=BEVEL_ON UNDERLAY_INNER GLOW_ON UNITY_UI_CLIP_RECT UNITY_UI_ALPHACLIP UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435488 lang=0 type=Fragment platform=metal reqs=227 mask=6 start=114 ok=1 outsize=4559

