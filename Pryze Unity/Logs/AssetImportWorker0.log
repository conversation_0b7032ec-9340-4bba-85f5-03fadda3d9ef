Unity Editor version:    6000.0.23f1 (1c4764c07fb4)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
-logFile
Logs/AssetImportWorker0.log
-srvPort
53700
-job-worker-count
4
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8782536448]  Target information:

Player connection [8782536448]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2525233127 [EditorId] 2525233127 [Version] 1048832 [Id] OSXEditor(0,MacBooks-MacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8782536448] Host joined multi-casting on [***********:54997]...
Player connection [8782536448] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 4
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 14.47 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.23f1 (1c4764c07fb4)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 Pro (high power)
Metal devices available: 1
0: Apple M1 Pro (high power)
Using device Apple M1 Pro (high power)
Initializing Metal device caps: Apple M1 Pro
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56345
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004473 seconds.
- Loaded All Assemblies, in  0.296 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 83 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.589 seconds
Domain Reload Profiling: 885ms
	BeginReloadAssembly (89ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (133ms)
		LoadAssemblies (89ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (130ms)
			TypeCache.Refresh (129ms)
				TypeCache.ScanAssembly (117ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (590ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (541ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (222ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (5ms)
			ProcessInitializeOnLoadAttributes (184ms)
			ProcessInitializeOnLoadMethodAttributes (107ms)
			AfterProcessingInitializeOnLoad (19ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  1.230 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.96 ms, found 5 plugins.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.42 ms, found 5 plugins.
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.183 seconds
Domain Reload Profiling: 2405ms
	BeginReloadAssembly (455ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (709ms)
		LoadAssemblies (448ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (225ms)
				TypeCache.ScanAssembly (202ms)
			BuildScriptInfoCaches (60ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1184ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (959ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (777ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.35 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 370 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9697 unused Assets / (5.1 MB). Loaded Objects now: 10440.
Memory consumption went from 232.4 MB to 227.3 MB.
Total: 9.510875 ms (FindLiveObjects: 1.159250 ms CreateObjectMapping: 0.393958 ms MarkObjects: 5.518375 ms  DeleteObjects: 2.438750 ms)

========================================================================
Received Import Request.
  Time since last request: 203656.578050 seconds.
  path: Assets/Scripts/ScriptableObjects/PlatformConfig.asset
  artifactKey: Guid(d7ecc33ef532f46eaa7ce6585f5ee640) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/ScriptableObjects/PlatformConfig.asset using Guid(d7ecc33ef532f46eaa7ce6585f5ee640) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5108b8e92582e5a9de3daa5e961a1517') in 0.0855745 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 10.36 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 14 unused Assets / (2.3 MB). Loaded Objects now: 10443.
Memory consumption went from 169.7 MB to 167.5 MB.
Total: 23.951417 ms (FindLiveObjects: 0.407333 ms CreateObjectMapping: 0.169875 ms MarkObjects: 22.957542 ms  DeleteObjects: 0.415792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fb9f000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.750 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.92 ms, found 5 plugins.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.53 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.990 seconds
Domain Reload Profiling: 1743ms
	BeginReloadAssembly (171ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (323ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (990ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (730ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (597ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.76 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 110 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (4.9 MB). Loaded Objects now: 10461.
Memory consumption went from 206.5 MB to 201.6 MB.
Total: 8.454250 ms (FindLiveObjects: 0.545750 ms CreateObjectMapping: 0.511167 ms MarkObjects: 5.071917 ms  DeleteObjects: 2.324834 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 87.000852 seconds.
  path: Assets/Scripts/ScriptableObjects/PlatformConfig.asset
  artifactKey: Guid(d7ecc33ef532f46eaa7ce6585f5ee640) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/ScriptableObjects/PlatformConfig.asset using Guid(d7ecc33ef532f46eaa7ce6585f5ee640) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f7e68ddfd457f59d754577038e84201') in 0.017316333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fbd3000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.829 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 2.26 ms, found 5 plugins.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.14 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.017 seconds
Domain Reload Profiling: 1851ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (567ms)
		LoadAssemblies (358ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (248ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (210ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1017ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (746ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (613ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.34 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (5.5 MB). Loaded Objects now: 10466.
Memory consumption went from 201.3 MB to 195.8 MB.
Total: 8.818625 ms (FindLiveObjects: 0.522875 ms CreateObjectMapping: 0.420459 ms MarkObjects: 4.924166 ms  DeleteObjects: 2.950125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 690.499011 seconds.
  path: Assets/Scripts/ScriptableObjects/PlatformConfig.asset
  artifactKey: Guid(d7ecc33ef532f46eaa7ce6585f5ee640) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/ScriptableObjects/PlatformConfig.asset using Guid(d7ecc33ef532f46eaa7ce6585f5ee640) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04ca18b0c9473bc9d51edc02f4e555b5') in 0.016630334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x33e06f000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.829 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 2.05 ms, found 5 plugins.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.42 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.009 seconds
Domain Reload Profiling: 1842ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (554ms)
		LoadAssemblies (348ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (246ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (211ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1009ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (746ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (604ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.59 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (5.5 MB). Loaded Objects now: 10471.
Memory consumption went from 201.3 MB to 195.9 MB.
Total: 9.408125 ms (FindLiveObjects: 0.522542 ms CreateObjectMapping: 0.432041 ms MarkObjects: 5.670417 ms  DeleteObjects: 2.782583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fa87000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.834 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.86 ms, found 5 plugins.
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.10 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.036 seconds
Domain Reload Profiling: 1875ms
	BeginReloadAssembly (203ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (578ms)
		LoadAssemblies (371ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (248ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (208ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1036ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (780ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (62ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (588ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.68 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (5.6 MB). Loaded Objects now: 10476.
Memory consumption went from 201.2 MB to 195.6 MB.
Total: 9.236334 ms (FindLiveObjects: 0.551417 ms CreateObjectMapping: 0.362125 ms MarkObjects: 5.349458 ms  DeleteObjects: 2.972708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fa87000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.798 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.78 ms, found 5 plugins.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.33 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.022 seconds
Domain Reload Profiling: 1824ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (539ms)
		LoadAssemblies (330ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (207ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1022ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (759ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (601ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.32 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (5.8 MB). Loaded Objects now: 10481.
Memory consumption went from 201.0 MB to 195.2 MB.
Total: 9.271708 ms (FindLiveObjects: 0.549750 ms CreateObjectMapping: 0.480292 ms MarkObjects: 5.126750 ms  DeleteObjects: 3.114125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fa87000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.837 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.91 ms, found 5 plugins.
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.10 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.991 seconds
Domain Reload Profiling: 1832ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (577ms)
		LoadAssemblies (359ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (220ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (991ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (739ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (591ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.63 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (5.6 MB). Loaded Objects now: 10486.
Memory consumption went from 201.2 MB to 195.6 MB.
Total: 9.317625 ms (FindLiveObjects: 0.492750 ms CreateObjectMapping: 0.372250 ms MarkObjects: 5.747958 ms  DeleteObjects: 2.704416 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fa87000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.853 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 2.23 ms, found 5 plugins.
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.50 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.992 seconds
Domain Reload Profiling: 1849ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (600ms)
		LoadAssemblies (385ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (251ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (208ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (992ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (736ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (596ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.71 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (5.4 MB). Loaded Objects now: 10491.
Memory consumption went from 201.2 MB to 195.8 MB.
Total: 8.775833 ms (FindLiveObjects: 0.506042 ms CreateObjectMapping: 0.383291 ms MarkObjects: 5.359834 ms  DeleteObjects: 2.526000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4653.568521 seconds.
  path: Assets/Scripts/ScriptableObjects/AppMetaData.asset
  artifactKey: Guid(d8815d65d9059334d9165c84b8dd94f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/ScriptableObjects/AppMetaData.asset using Guid(d8815d65d9059334d9165c84b8dd94f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9399c259ef883fcd430ec7386a032bf8') in 0.100330875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16c0eb000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.866 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.76 ms, found 5 plugins.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.30 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.001 seconds
Domain Reload Profiling: 1871ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (590ms)
		LoadAssemblies (371ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (224ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1001ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (751ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (610ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.20 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (5.6 MB). Loaded Objects now: 10496.
Memory consumption went from 201.2 MB to 195.6 MB.
Total: 9.189250 ms (FindLiveObjects: 0.553125 ms CreateObjectMapping: 0.425166 ms MarkObjects: 4.838875 ms  DeleteObjects: 3.371166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 228.017672 seconds.
  path: Assets/Scenes/Menu.unity
  artifactKey: Guid(1c571c32af2e801438c3601de6fa2b68) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Menu.unity using Guid(1c571c32af2e801438c3601de6fa2b68) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84c7cedc4486d9bfe4d21029cd7c929d') in 0.000979917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 55.247665 seconds.
  path: Assets/Scenes/SplashScreen.unity
  artifactKey: Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SplashScreen.unity using Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '267c98e984c9ad15c168823d91d4416e') in 0.0017415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16c0eb000 may have been prematurely finalized
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  0.906 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.84 ms, found 5 plugins.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.28 ms, found 5 plugins.
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.007 seconds
Domain Reload Profiling: 1917ms
	BeginReloadAssembly (205ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (643ms)
		LoadAssemblies (398ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (287ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1007ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (745ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (605ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.81 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 100 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9695 unused Assets / (6.0 MB). Loaded Objects now: 10501.
Memory consumption went from 201.1 MB to 195.1 MB.
Total: 13.281334 ms (FindLiveObjects: 0.544750 ms CreateObjectMapping: 0.425042 ms MarkObjects: 9.166000 ms  DeleteObjects: 3.145333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 225.648599 seconds.
  path: Assets/Games/BrickBreaker/Art Works/Scores/+10.png
  artifactKey: Guid(cb1ed24940d61564f81141304380cfa6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/BrickBreaker/Art Works/Scores/+10.png using Guid(cb1ed24940d61564f81141304380cfa6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcd9b3df470ce63922a604b917f80de4') in 0.141385041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Games/KnifeHit/BombSeq/0002.png
  artifactKey: Guid(09d91775495094affb9422b22b0d7619) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/KnifeHit/BombSeq/0002.png using Guid(09d91775495094affb9422b22b0d7619) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '279bfa10b05516edd7e0f469778ec43a') in 0.008476667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Games/BrickBreaker/Art Works/Scores/+30.png
  artifactKey: Guid(b09c3069814b1184fa65c523daa3dfac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/BrickBreaker/Art Works/Scores/+30.png using Guid(b09c3069814b1184fa65c523daa3dfac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd417b3b0b42a91e44aa67f3509c957b') in 0.008249583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/UI/WalletTutorial/Easy Paisa/2.png
  artifactKey: Guid(be410d1543b59a34e83203f7d5aba135) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/WalletTutorial/Easy Paisa/2.png using Guid(be410d1543b59a34e83203f7d5aba135) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9f7f262519c6a867ee2ae33f8a93ac2') in 0.007131791 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Games/Stumps/Cricket Game Ui Atlas/Screen-Images-Seq/2.png
  artifactKey: Guid(a1793bc7b91924d2693698014297a7f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Stumps/Cricket Game Ui Atlas/Screen-Images-Seq/2.png using Guid(a1793bc7b91924d2693698014297a7f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ad8dc981226956270f8b329f25a6e05') in 0.008627792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Games/Stumps/Cricket Game Ui Atlas/Score-Numbers and stats/Atlas/All-Score-Texts-Atlas-Outer-Glow.png
  artifactKey: Guid(8f4d767d17bc74d4d91f5fd6b4a62b94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Stumps/Cricket Game Ui Atlas/Score-Numbers and stats/Atlas/All-Score-Texts-Atlas-Outer-Glow.png using Guid(8f4d767d17bc74d4d91f5fd6b4a62b94) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4d075b163dcac54fd47ee925f7dc1cc6') in 0.051116166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Games/Blocks/Images/001.png
  artifactKey: Guid(cb336e9dc93e5c144b41770ec6ba3fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Blocks/Images/001.png using Guid(cb336e9dc93e5c144b41770ec6ba3fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74080db01f6325c5ca0e74cf25fc999a') in 0.00759275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/UI/WalletTutorial/Easy Paisa/1.png
  artifactKey: Guid(ebecd7a962dc57243a19e8d85c22014b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/WalletTutorial/Easy Paisa/1.png using Guid(ebecd7a962dc57243a19e8d85c22014b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ca936c455dcffd9fa38d5d505bc3835b') in 0.008806792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Games/BrickBreaker/Art Works/Scores/+70.png
  artifactKey: Guid(417a648e2fefc244a860e4d4193f8aef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/BrickBreaker/Art Works/Scores/+70.png using Guid(417a648e2fefc244a860e4d4193f8aef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67d5f5deb0f9a7dad3237229d66c9e59') in 0.006285042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Games/KnifeHit/BombSeq/0001.png
  artifactKey: Guid(beaad6979004b49498f08068c3a0e81d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/KnifeHit/BombSeq/0001.png using Guid(beaad6979004b49498f08068c3a0e81d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21ebb9b8902e8b47c770183409609996') in 0.008551166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/Games/BrickBreaker/Art Works/Scores/+80.png
  artifactKey: Guid(867171d678d0f5546827dddea5d709cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/BrickBreaker/Art Works/Scores/+80.png using Guid(867171d678d0f5546827dddea5d709cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd9b0568bf937abad8502909bfdfb02a9') in 0.00724925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Games/Stumps/Cricket Game Ui Atlas/Intro-Scene-Updated (1)/1.png
  artifactKey: Guid(1c9dd5962affe4e73aca694e3f200ef8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Stumps/Cricket Game Ui Atlas/Intro-Scene-Updated (1)/1.png using Guid(1c9dd5962affe4e73aca694e3f200ef8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9429a8e5cebf579b3b0a680dec079bf8') in 0.0243555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 44

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Games/BrickBreaker/Art Works/Scores/+60.png
  artifactKey: Guid(3069f1d9156d52142b6ec70d189e6ab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/BrickBreaker/Art Works/Scores/+60.png using Guid(3069f1d9156d52142b6ec70d189e6ab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5eceb97ad2f3267efcefe6fd38409df') in 0.0115885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Games/ColorBounce/Art/Player sprites/1.png
  artifactKey: Guid(298be7da7e3df2d4cadad042f9f8ec91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/ColorBounce/Art/Player sprites/1.png using Guid(298be7da7e3df2d4cadad042f9f8ec91) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e288a4eab2dbefc4fb3ed1ec6caf3177') in 0.007852917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Games/Stumps/Cricket Game Ui Atlas/Intro-Scene-Updated (1)/2.png
  artifactKey: Guid(e17f3073dec5c4845a53a07291372c3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Stumps/Cricket Game Ui Atlas/Intro-Scene-Updated (1)/2.png using Guid(e17f3073dec5c4845a53a07291372c3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '880d562591e5c3ebf367ca27795e1663') in 0.016627917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 44

========================================================================
Received Import Request.
  Time since last request: 0.475664 seconds.
  path: Assets/Games/HoopRush/Sprite/Basket-Ball-Game-Ui/5pts.png
  artifactKey: Guid(cac3ecd4f58fc4f99b38c44dc8135ecc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/HoopRush/Sprite/Basket-Ball-Game-Ui/5pts.png using Guid(cac3ecd4f58fc4f99b38c44dc8135ecc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6008df0df5661849e9e0ffe1626514f6') in 0.016766959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 41

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Games/HoopRush/Sprite/Basket-Ball-Game-Ui/20pts.png
  artifactKey: Guid(6215e505517354572926c0de0262dcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/HoopRush/Sprite/Basket-Ball-Game-Ui/20pts.png using Guid(6215e505517354572926c0de0262dcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'acd03bdafc8339d2648ebbb48a3ca5f2') in 0.010675792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 41

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Games/FruitFrenzy/Sprites/Fruit_Ninja_Assets/All_Frutis_Atlas_02.png
  artifactKey: Guid(2173db6bcb1054076962e71cd73e1eb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/FruitFrenzy/Sprites/Fruit_Ninja_Assets/All_Frutis_Atlas_02.png using Guid(2173db6bcb1054076962e71cd73e1eb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1c2f83f510009f37a24e3a95694cbc70') in 0.151379125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 51

========================================================================
Received Import Request.
  Time since last request: 0.059379 seconds.
  path: Assets/Games/Stumps/Cricket Game Ui Atlas/Intro-Scene-Updated (1)/Match-Summary/Match-Summary-Container.png
  artifactKey: Guid(eb6601ba3de6147e49a603cef03c92a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Stumps/Cricket Game Ui Atlas/Intro-Scene-Updated (1)/Match-Summary/Match-Summary-Container.png using Guid(eb6601ba3de6147e49a603cef03c92a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b0c88b559f94aa7b4fb9837081ab3b8') in 0.013529375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 44

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/UI/Demo Screens/Icons/ResultIcon.svg
  artifactKey: Guid(88da98e229cf8f94e90ebf8ebf1e21aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Demo Screens/Icons/ResultIcon.svg using Guid(88da98e229cf8f94e90ebf8ebf1e21aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd6460169bbc4b3f2a8d4fd4c87de0df5') in 0.238380917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
