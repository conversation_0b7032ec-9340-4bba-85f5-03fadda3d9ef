Unity Editor version:    6000.0.23f1 (1c4764c07fb4)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
-logFile
Logs/AssetImportWorker2.log
-srvPort
53700
-job-worker-count
4
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8782536448]  Target information:

Player connection [8782536448]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 987531707 [EditorId] 987531707 [Version] 1048832 [Id] OSXEditor(0,MacBooks-MacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8782536448] Host joined multi-casting on [***********:54997]...
Player connection [8782536448] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 4
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 18.90 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.23f1 (1c4764c07fb4)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 Pro (high power)
Metal devices available: 1
0: Apple M1 Pro (high power)
Using device Apple M1 Pro (high power)
Initializing Metal device caps: Apple M1 Pro
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56726
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.005007 seconds.
- Loaded All Assemblies, in  0.324 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 216 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.748 seconds
Domain Reload Profiling: 1072ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (136ms)
		LoadAssemblies (113ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (133ms)
			TypeCache.Refresh (131ms)
				TypeCache.ScanAssembly (119ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (748ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (697ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (376ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (6ms)
			ProcessInitializeOnLoadAttributes (192ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'Newtonsoft.Json.dll' with different versions detected, using 'Packages/jillejr.newtonsoft.json-for-unity/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed' and ignoring 'Assets/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll, AssemblyName=Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=null'.- Loaded All Assemblies, in  1.369 seconds
Script 'RotationConstraint' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 2.33 ms, found 5 plugins.
OnLevelWasLoaded was found on SoundController
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on GameModel
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Refreshing native plugins compatible for Editor in 2.57 ms, found 5 plugins.
Missing types referenced from component UniversalRenderPipelineGlobalSettings on game object UniversalRenderPipelineGlobalSettings:
	UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets, Unity.RenderPipelines.Universal.Runtime (1 object)
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:142)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.153 seconds
Domain Reload Profiling: 2513ms
	BeginReloadAssembly (616ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (687ms)
		LoadAssemblies (416ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (314ms)
			TypeCache.Refresh (229ms)
				TypeCache.ScanAssembly (201ms)
			BuildScriptInfoCaches (62ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1153ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (935ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (753ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.23f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.06 seconds
Refreshing native plugins compatible for Editor in 2.62 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 370 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9697 unused Assets / (6.4 MB). Loaded Objects now: 10440.
Memory consumption went from 232.7 MB to 226.4 MB.
Total: 14.544875 ms (FindLiveObjects: 5.736792 ms CreateObjectMapping: 0.333333 ms MarkObjects: 5.177458 ms  DeleteObjects: 3.297000 ms)

========================================================================
Received Import Request.
  Time since last request: 209705.172073 seconds.
  path: Assets/UI/Sprites/question-mark-circle.png
  artifactKey: Guid(2087b56f9a759374bb87ef7db5e4254c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/question-mark-circle.png using Guid(2087b56f9a759374bb87ef7db5e4254c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4337927e337afabf8c4494e26222c2f9') in 0.060345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 7.034841 seconds.
  path: Assets/UI/Sprites/support_icon.png
  artifactKey: Guid(2087b56f9a759374bb87ef7db5e4254c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/support_icon.png using Guid(2087b56f9a759374bb87ef7db5e4254c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9679269a1fa9fa493cbdc1a25afa0454') in 0.00795525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 3457.404762 seconds.
  path: Assets/UI/Sprites/SearchIcon.png
  artifactKey: Guid(e6fd0db6607d389489e7fa00b2d71c0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/SearchIcon.png using Guid(e6fd0db6607d389489e7fa00b2d71c0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b156106f06987c1bd42653dc1598800') in 0.058050708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 1.576931 seconds.
  path: Assets/UI/Sprites/support_icon.png
  artifactKey: Guid(af703f9fd6fb349ad93ad8c150df8b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/support_icon.png using Guid(af703f9fd6fb349ad93ad8c150df8b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4111efcd72a788d686390e89b5a11848') in 0.007368292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 10.302301 seconds.
  path: Assets/UI/Sprites/support_icon.png
  artifactKey: Guid(af703f9fd6fb349ad93ad8c150df8b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/support_icon.png using Guid(af703f9fd6fb349ad93ad8c150df8b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b120e36d16767fa0197d26eb6ce27e6') in 0.010859875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 45.006846 seconds.
  path: Assets/Games/Stumps/Cricket Game Ui Atlas/Power-Bar/Power-Bar 1.png
  artifactKey: Guid(6e3a8de077ff4444b8918f8100ca151b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Stumps/Cricket Game Ui Atlas/Power-Bar/Power-Bar 1.png using Guid(6e3a8de077ff4444b8918f8100ca151b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a37118a2d42ae22f7ad405626782d3c') in 0.013829042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/UI/Pryze_coin.png
  artifactKey: Guid(237393b592d7e7c4c877c120d001ed6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Pryze_coin.png using Guid(237393b592d7e7c4c877c120d001ed6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '69289775c5cb48fb7a65a0cb16cc48ab') in 0.010010042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/UI/TutorialUI/Rectangle 2 copy 2.png
  artifactKey: Guid(e84198e3d1805d549bf3affce1b7d995) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/TutorialUI/Rectangle 2 copy 2.png using Guid(e84198e3d1805d549bf3affce1b7d995) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c970a4b94fbd86af67b5b9281213a774') in 0.011423875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/UI/Sprites/Rectangle 3.png
  artifactKey: Guid(b450941be5d84284984aab2dbff915e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/Rectangle 3.png using Guid(b450941be5d84284984aab2dbff915e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '489c26ad09e2c7b8cb92678c2dad61ec') in 0.008657792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/UI/Sprites/Rectangle 2.png
  artifactKey: Guid(bbc155eb66efb1349962a8b78cc818bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/Rectangle 2.png using Guid(bbc155eb66efb1349962a8b78cc818bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8350caee8613089227140df56d37e6d3') in 0.007849667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Games/Carrom/3dAssets/Carrom new Assets and UI/Carrom_remaining_assets/Lower_Slider_Container/Red.png
  artifactKey: Guid(05bb49c90d506418ab6c5557cba1f8a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Carrom/3dAssets/Carrom new Assets and UI/Carrom_remaining_assets/Lower_Slider_Container/Red.png using Guid(05bb49c90d506418ab6c5557cba1f8a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e95cb3f37e793926000d6dc5b958989b') in 0.01062725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/UI/ErrorPopUp-Ui/App Update_Screens/Rectangle 68.png
  artifactKey: Guid(eecd91a29ef1b463495162e40f335198) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/ErrorPopUp-Ui/App Update_Screens/Rectangle 68.png using Guid(eecd91a29ef1b463495162e40f335198) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '384b58120231ca982df2cd3007c5bd79') in 0.009669416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/UI/Pryze_Splash_Image.png
  artifactKey: Guid(f0ff2d5aa2e6b804e9c5c091161cea3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Pryze_Splash_Image.png using Guid(f0ff2d5aa2e6b804e9c5c091161cea3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6c8665b002da3ee34a5b172683a0fdc6') in 0.007794292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Le Tai's Asset/TrueShadow/Demo/Full Colors/Sprites/red-triangle-dark.png
  artifactKey: Guid(a99a043c55e7b414faf59239189fe6f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Le Tai's Asset/TrueShadow/Demo/Full Colors/Sprites/red-triangle-dark.png using Guid(a99a043c55e7b414faf59239189fe6f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c850df796ed4ba24a9cbbd2052c0bfd4') in 0.009096125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Games/BrickBreaker/Art Works/Protractor.png
  artifactKey: Guid(41c1bcd22cab44edab9e48f338ee0ef1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/BrickBreaker/Art Works/Protractor.png using Guid(41c1bcd22cab44edab9e48f338ee0ef1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c6a70bfa2437c4413f091195ec02f8cb') in 0.008708375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/UI/TutorialUI/Rectangle 1.png
  artifactKey: Guid(76736b46768c86c4d97ac39451f586b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/TutorialUI/Rectangle 1.png using Guid(76736b46768c86c4d97ac39451f586b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e5acb5098eeb92deba0e37d668242672') in 0.010388458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/UI/splash screen/Rectangle 1 copy 2.png
  artifactKey: Guid(a70b30c941f319e43a338772d5e954bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/splash screen/Rectangle 1 copy 2.png using Guid(a70b30c941f319e43a338772d5e954bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '242a42fa1b39240af9e8183390be6af1') in 0.008300125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/UI/Sprites/PurpleButtonSource.png
  artifactKey: Guid(f3d5bc692c6ceeb4ba34f67f49af5e0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/PurpleButtonSource.png using Guid(f3d5bc692c6ceeb4ba34f67f49af5e0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3243f798394afd6b699221900db208d2') in 0.008553792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/UI/Sprites/PurpleCircle 1.png
  artifactKey: Guid(029e7c4074bbc2a408c627435dd2f87b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/PurpleCircle 1.png using Guid(029e7c4074bbc2a408c627435dd2f87b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '11466a96dbfaa2d4bc6a7e731bb8a196') in 0.009689416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/UI/Sprites/Rectangle 34624901.png
  artifactKey: Guid(1145b2112d4c2cd41a8df0ed0dc1af3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Sprites/Rectangle 34624901.png using Guid(1145b2112d4c2cd41a8df0ed0dc1af3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '11beca09639e2249090705ef8eacdbff') in 0.010934334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/UI/TutorialUI/Rectangle 33.png
  artifactKey: Guid(e3f244e9901252741aa71e15675092b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/TutorialUI/Rectangle 33.png using Guid(e3f244e9901252741aa71e15675092b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db8b66b678155950efee49a04ef2ed29') in 0.007536916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Le Tai's Asset/TrueShadow/Demo/Full Colors/Sprites/red-triangle.png
  artifactKey: Guid(79ec4f34ced503e44b6f83ba713c0d6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Le Tai's Asset/TrueShadow/Demo/Full Colors/Sprites/red-triangle.png using Guid(79ec4f34ced503e44b6f83ba713c0d6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6194163a8efcaf45729263c2303b5a29') in 0.009647334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Games/Carrom/3dAssets/Carrom new Assets and UI/Carrom_remaining_assets/Lower_Slider_Container/Purple.png
  artifactKey: Guid(4b8d569b993d34d20a337de6b2e9283a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Carrom/3dAssets/Carrom new Assets and UI/Carrom_remaining_assets/Lower_Slider_Container/Purple.png using Guid(4b8d569b993d34d20a337de6b2e9283a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0960e1163dcfde5d63bc292aed45b53a') in 0.011231042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/UI/TutorialUI/Rectangle 3.png
  artifactKey: Guid(4162b431a04fde048bfd41ccb6fe85c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/TutorialUI/Rectangle 3.png using Guid(4162b431a04fde048bfd41ccb6fe85c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c59fd8086ea5825c437003f848d59543') in 0.010056875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Games/BrickBreaker/Art Works/Menu elements/Q.png
  artifactKey: Guid(f568ffa4aa2093547b58e0f0da61de5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/BrickBreaker/Art Works/Menu elements/Q.png using Guid(f568ffa4aa2093547b58e0f0da61de5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7da69fe8ba66333289cf5ae9b0d5e8d4') in 0.009142291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/UI/Home_Page_Banners/Red_Bull.png
  artifactKey: Guid(0d98cd9cbecee224faa5ff34bf0d248b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/Home_Page_Banners/Red_Bull.png using Guid(0d98cd9cbecee224faa5ff34bf0d248b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b6fcf3233e814cc11fcc4d00a8e2e869') in 0.00844425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Games/StackUp/Art work/UI/sprites/rate.png
  artifactKey: Guid(556e15b7c3ae0418d976f1e559bca97c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/StackUp/Art work/UI/sprites/rate.png using Guid(556e15b7c3ae0418d976f1e559bca97c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ca1a38b2c9205780a3a54f8251f02f5') in 0.008368125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.098731 seconds.
  path: Assets/Games/FruitFrenzy/Sprites/New Fruits/Watermelon/Watermelon_Slice_4.png
  artifactKey: Guid(66d4f042fd8d115468197a58f05e57d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/FruitFrenzy/Sprites/New Fruits/Watermelon/Watermelon_Slice_4.png using Guid(66d4f042fd8d115468197a58f05e57d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a8d0e11ea536878555445e01ac974da2') in 0.008259125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Everybody (Backstreet’s Back)” by the Backstreet Boys.png
  artifactKey: Guid(4ba3b76d312162d4ca33fa8a784f5949) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Everybody (Backstreet’s Back)” by the Backstreet Boys.png using Guid(4ba3b76d312162d4ca33fa8a784f5949) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea1d4eb61eae09877ecdad32a8df6620') in 0.010193125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000135 seconds.
  path: Assets/Games/ColorBounce/_Documentation/assets/blueprint-css/plugins/link-icons/icons/xls.png
  artifactKey: Guid(6a4fc26e6221567429244cd52b309abc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/ColorBounce/_Documentation/assets/blueprint-css/plugins/link-icons/icons/xls.png using Guid(6a4fc26e6221567429244cd52b309abc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e128f9030a2746a1ba7f0013c4672269') in 0.0093445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000214 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“When Doves Cry” by Prince.png
  artifactKey: Guid(f34219b93224f3940aa62c8e4ac31fef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“When Doves Cry” by Prince.png using Guid(f34219b93224f3940aa62c8e4ac31fef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c67dcc19e971768aba9166ad3197f1c5') in 0.008841542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Games/Pool/Sprites/UI/X3_stick.png
  artifactKey: Guid(eb77c7b85f2814e9cbf0ab4b43bb3b7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Pool/Sprites/UI/X3_stick.png using Guid(eb77c7b85f2814e9cbf0ab4b43bb3b7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '39a5a3beb3b91bc42c1716ec468e8f5a') in 0.007316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Genie in a Bottle” by Christina Aguilera.png
  artifactKey: Guid(39e675b90d2186c4f8a5c29e8e7103c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Genie in a Bottle” by Christina Aguilera.png using Guid(39e675b90d2186c4f8a5c29e8e7103c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '470a9e4b44187194b6bdd67169909089') in 0.010095417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“I Wanna Dance With Somebody” by Whitney Houston.png
  artifactKey: Guid(1b1591d064acd444eb40619a731c87f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“I Wanna Dance With Somebody” by Whitney Houston.png using Guid(1b1591d064acd444eb40619a731c87f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b151a1e590c62ff0b576e943db9c0a3') in 0.009434291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Hollaback Girl” by Gwen Stefani.png
  artifactKey: Guid(2e5557bb772c14946ba93af6675d03e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Hollaback Girl” by Gwen Stefani.png using Guid(2e5557bb772c14946ba93af6675d03e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'df0e20d30e41632593df2b208b829a21') in 0.00881925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“It’s Gonna Be Me” by ‘N Sync.png
  artifactKey: Guid(05f51abc14c9b0748a68be5feaabe14b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“It’s Gonna Be Me” by ‘N Sync.png using Guid(05f51abc14c9b0748a68be5feaabe14b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61ef6a9c3d5c75f745a717b667c450d0') in 0.009184917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Games/Pool/Sprites/DontEdit/whiteBallLimits.png
  artifactKey: Guid(1cbc1ece62bf4460fb30f2002c6558a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Games/Pool/Sprites/DontEdit/whiteBallLimits.png using Guid(1cbc1ece62bf4460fb30f2002c6558a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9d75bbde6e231b93a1d6127993db6f39') in 0.008282916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Call Me Maybe” by Carly Rae Jepsen.png
  artifactKey: Guid(273b3f8c3b43e0b458f1ab0c96f72ea3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Call Me Maybe” by Carly Rae Jepsen.png using Guid(273b3f8c3b43e0b458f1ab0c96f72ea3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e3f69bffe67405fe4ae0055e036e3364') in 0.009364334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Single Ladies (Put a Ring on It)” by Beyoncé.png
  artifactKey: Guid(a90c92ab62d4d7546ba6a6d2a09227db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simple Scroll-Snap/Examples/Example 1 (macOS Finder)/Sprites/Songs/“Single Ladies (Put a Ring on It)” by Beyoncé.png using Guid(a90c92ab62d4d7546ba6a6d2a09227db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd32087ff4e1118ddc113b9a6a7fa29c0') in 0.007780125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
