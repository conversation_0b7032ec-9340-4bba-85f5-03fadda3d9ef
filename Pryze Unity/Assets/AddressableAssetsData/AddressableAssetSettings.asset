%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: 86a06d8726f4b43639cd102964972156
  m_currentHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 1
  m_BundleLocalCatalog: 0
  m_CatalogRequestsTimeout: 600
  m_DisableCatalogUpdateOnStart: 0
  m_InternalIdNamingMode: 0
  m_InternalBundleIdMode: 1
  m_AssetLoadMode: 0
  m_BundledAssetProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider
  m_AssetBundleProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 1
  m_EnableJsonCatalog: 1
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_UseUWRForLocalBundles: 0
  m_BundleTimeout: 0
  m_BundleRetryCount: 0
  m_BundleRedirectLimit: -1
  m_SharedBundleSettings: 0
  m_SharedBundleSettingsCustomGroupIndex: 0
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 1
  m_DisableVisibleSubAssetRepresentations: 0
  m_BuiltInBundleNaming: 0
  mBuiltInBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_CheckForContentUpdateRestrictionsOption: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: a1b25f6e21eaf430a82ec27c0e87b85f
  m_RemoteCatalogLoadPath:
    m_Id: 6f6cd188247b347e98110bd4db0c6001
  m_ContentStateBuildPathProfileVariableName: Remote.BuildPath
  m_CustomContentStateBuildPath: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 2
  m_overridePlayerVersion: '[UnityEditor.PlayerSettings.bundleVersion]'
  m_GroupAssets:
  - {fileID: 11400000, guid: 2053e903c453246f493866df58d477ea, type: 2}
  m_BuildSettings:
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: 3dd2b3d0fc7a64f44b96c860a5a1a018
      m_ProfileName: Default
      m_Values:
      - m_Id: 37dfb4ca1a605450480be47c2f67b419
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: 6f6cd188247b347e98110bd4db0c6001
        m_Value: <undefined>
      - m_Id: 71ed69fe39ec14276acb6923e5893f75
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: a1b25f6e21eaf430a82ec27c0e87b85f
        m_Value: 'ServerData/[BuildTarget]'
      - m_Id: b52431480da4a40829f72bdabf5d021b
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
    - m_InheritedParent: 
      m_Id: 5bb18a5b8913c4d338a0c5180b17d567
      m_ProfileName: Production
      m_Values:
      - m_Id: 37dfb4ca1a605450480be47c2f67b419
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: 6f6cd188247b347e98110bd4db0c6001
        m_Value: https://pryze-unity-addressables.s3-accelerate.amazonaws.com/Android/Production/version_1.1.6
      - m_Id: 71ed69fe39ec14276acb6923e5893f75
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: a1b25f6e21eaf430a82ec27c0e87b85f
        m_Value: ServerData/Production/[BuildTarget]/Production/version_1.1.6
      - m_Id: b52431480da4a40829f72bdabf5d021b
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
    m_ProfileEntryNames:
    - m_Id: 37dfb4ca1a605450480be47c2f67b419
      m_Name: BuildTarget
      m_InlineUsage: 0
    - m_Id: 6f6cd188247b347e98110bd4db0c6001
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    - m_Id: 71ed69fe39ec14276acb6923e5893f75
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: a1b25f6e21eaf430a82ec27c0e87b85f
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: b52431480da4a40829f72bdabf5d021b
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
    - ZigRun
    - SnakeDash
    - KnifeFlick
    - ColorBounce
    - StackUp
    - MainMenu
    - Pool
    - SpaceJumper
    - FruitSlash
    - Carrom
    - Stumps
    - HoopRush
    - BrickBreaker
    - BlockSmasher
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: cf037a48218f54427860d928e483a224, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 2
  m_DataBuilders:
  - {fileID: 11400000, guid: eaae494c7d43142d986e2c71c0f82fbc, type: 2}
  - {fileID: 11400000, guid: 005e132bd6271407d803a9ab07b6a991, type: 2}
  - {fileID: 11400000, guid: 38f626a5e471846e9aced35e89c3d6eb, type: 2}
  m_ActiveProfileId: 5bb18a5b8913c4d338a0c5180b17d567
