using DG.Tweening;
using Game.Utilities;
using Pryze.Managers;
using System.Collections;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.UI;

namespace Pryze.UI
{
    public class EnterUsername : AssetLoader, ITransition, IWebRequestResponse, IClose
    {
        WebRequestManager APIManager => DependencyManager.instance.WebRequestManager;
        MainMenu MainMenu => DependencyManager.instance.MainMenu;
        PlayerStateManager StatsManager => DependencyManager.instance.PlayerStateManager;
        FirebaseManager AnalyticsManager => DependencyManager.instance.FirebaseManager;
        MetadataDownloaderComponent Metadata => DependencyManager.instance.Metadata;
        public TMP_InputField nameField;
        [SerializeField] Button registerButton;
        [SerializeField] Button backButton;
        [SerializeField] Button termButton;
        [SerializeField] GameObject checkingStatus;
        [SerializeField] GameObject unavailableNameStatus;
        [SerializeField] GameObject availableNameStatus;
        [SerializeField] GameObject gamenowHeader;
        [SerializeField] bool allowClose;
        public bool AllowClose { get { return allowClose; } }

        public GameObject loadingPanel;

        float debounceTime = 0.5f;   // Debounce time (in seconds)
        private Coroutine debounceCoroutine;
        private string previousText = "";
        // Start is called once before the first execution of Update after the MonoBehaviour is created
        void Start()
        {
            nameField?.onValueChanged.AddListener(OnNameInputChanged);
            nameField?.onSelect.AddListener(OnInputfieldSelect);
            backButton?.onClick.AddListener(Back);
            termButton?.onClick.AddListener(openTerms);
            checkGamePlatform();
        }
        private void OnEnable()
        {
            nameField?.ActivateInputField();
            MainMenu.currentPanel.Add(gameObject);
            setupData();
            //AnalyticsManager.LogEvent("enter_user_screen_opened");
        }
        void checkGamePlatform()
        {
            switch (StatsManager.PlatformConfig.appPlatform)
            {
                case AppPlatform.WebGL:
                    gamenowHeader.SetActive(false);
                    break;
                case AppPlatform.Pro:
                case AppPlatform.iOS:
                case AppPlatform.PlayStore:
                    gamenowHeader.SetActive(true);
                    break;
            }
        }

        void setupData()
        { 
            nameField.text = StatsManager.PlayerStatSettings.userData.data.user_name;
        }
        void LateUpdate()
        {
            if (previousText != nameField.text) // Detect any changes
            {
                previousText = nameField.text;
                OnNameInputChanged(nameField.text); // Trigger manually
            }
        }
        private void OnInputfieldSelect(string input)
        {
            nameField.text = "";
        }
        private void OnNameInputChanged(string input)
        {
            registerButton.interactable = false;
            if (nameField.text.Length == 0)
            {
                checkingStatus.gameObject.SetActive(false);
                unavailableNameStatus.gameObject.SetActive(false);
                availableNameStatus.gameObject.SetActive(false);
                return;
            }
            // Stop any existing debounce coroutine before starting a new one
            if (debounceCoroutine != null)
            {
                StopCoroutine(debounceCoroutine);
            }
            // Start a new coroutine to debounce the API request
            debounceCoroutine = StartCoroutine(DebounceNameCheck(input));

        }
        private IEnumerator DebounceNameCheck(string input)
        {
            // Wait for the debounce time
            yield return new WaitForSeconds(debounceTime);
            checkUsernameAvailability();
        }
        void checkUsernameAvailability()
        {
            string url = APIManager.Config.ServerConfigs.FirstOrDefault(i => i.serverType == APIManager.Config.ServerType)?.baseURL + APIManager.Config.CommonAttributes.usernameCheck + nameField.text;
            APIManager.GetRequest(url, this);
            checkingStatus?.SetActive(true);
            unavailableNameStatus?.SetActive(false);
            availableNameStatus?.SetActive(false);
        }

        void openTerms()
        {
            MainMenu.Attributes.StaticTextDisplayPanel?.gameObject.SetActive(true);
            MainMenu.Attributes.StaticTextDisplayPanel.displayData("Privacy Policy", Metadata.AppMetaData.data.complianceAndLegal.privacy_policy);
        }
        void Back()
        {
            MainMenu.OnSceneChange?.Invoke(transform, MainMenu.Attributes.EnterReferalPanel.transform, TransitionType.SwipeRight);
        }
        public void MoveIn(TransitionType type)
        {
            //RectTransform rectTransform = GetComponent<RectTransform>();
            //switch (type)
            //{
            //    case TransitionType.SwipeLeft:
            //        rectTransform.anchoredPosition = new Vector2(Screen.width, rectTransform.anchoredPosition.y);
            //        break;
            //    case TransitionType.SwipeRight:
            //        rectTransform.anchoredPosition = new Vector2(-Screen.width, rectTransform.anchoredPosition.y);
            //        break;
            //}

            //// Move the panel into the screen from the left
            //rectTransform.DOAnchorPos(Vector2.zero, MainMenu.Attributes.TransitionDuration);
            gameObject.SetActive(true);
        }
        public void MoveOut(TransitionType type)
        {
            //switch (type)
            //{
            //    case TransitionType.SwipeLeft:
            //        GetComponent<RectTransform>().DOAnchorPos(new Vector2(-Screen.width, GetComponent<RectTransform>().anchoredPosition.y), MainMenu.Attributes.TransitionDuration)
            //    .OnComplete(() => gameObject.SetActive(false));
            //        break;
            //    case TransitionType.SwipeRight:
            //        GetComponent<RectTransform>().DOAnchorPos(new Vector2(Screen.width, GetComponent<RectTransform>().anchoredPosition.y), MainMenu.Attributes.TransitionDuration)
            //    .OnComplete(() => gameObject.SetActive(false));
            //        break;
            //}
            gameObject.SetActive(false);
        }
        public void OnRegistrationComplete()
        {
            MainMenu.OnSceneChange?.Invoke(transform, MainMenu.Attributes.MainMenuPanel.transform, TransitionType.SwipeLeft);
        }
        #region Response Handler
        public void OnRequestComplete(UnityWebRequest request, string response)
        {
            // Handle successful response
            Debug.Log("Request succeeded: " + response);

            NameAvailabilityResponse APIresponse = JsonUtility.FromJson<NameAvailabilityResponse>(response);

            // Ensure APIresponse is not null and handle the response
            if (APIresponse != null && APIresponse.success)
            {
                if (APIresponse.usernameAvailable)
                {
                    checkingStatus?.SetActive(false);
                    unavailableNameStatus?.SetActive(false);
                    availableNameStatus?.SetActive(true);
                    registerButton.interactable = true;
                }
                else
                {
                    checkingStatus?.SetActive(false);
                    if (string.IsNullOrEmpty(nameField.text))
                    {

                        unavailableNameStatus?.SetActive(false);
                    }
                    else
                    {
                        unavailableNameStatus?.SetActive(true);
                    }
                    availableNameStatus?.SetActive(false);
                }
            }
            else
            {
                // Handle the case where APIresponse is null or success is false
                Debug.LogWarning("API response was not successful or was null.");
                checkingStatus?.SetActive(false);
                unavailableNameStatus?.SetActive(true);
                availableNameStatus?.SetActive(false);
                registerButton.interactable = false;
            }
        }

        public void OnRequestFailed(UnityWebRequest request, string error)
        {
            // Handle failed request
            Debug.LogError("Request failed: " + error);
            checkingStatus?.SetActive(false);
            availableNameStatus?.SetActive(false);
            if (string.IsNullOrEmpty(nameField.text))
            {

                unavailableNameStatus?.SetActive(false);
            }
            else
            {
                unavailableNameStatus?.SetActive(true);
            }

            AuthResponse authResponse = JsonUtility.FromJson<AuthResponse>(error);
            if (authResponse.error == "Unauthorized access")
            {
                Metadata.sendRequestToGetMetaData();
                StatsManager.DeleteData();
                StatsManager.PlayerStatSettings.userData = null;
                MainMenu.ClearDataOnLogOut(this.transform);

            }
            if (authResponse.error == "Not allowed")
            { 
                unavailableNameStatus?.SetActive(true);
                availableNameStatus?.SetActive(false);
            }
        }
        #endregion


        private void OnDisable()
        {
            MainMenu.currentPanel.Remove(gameObject);
        }
        private void OnDestroy()
        {
            nameField?.onValueChanged.RemoveListener(OnNameInputChanged);
        }

        public void onEscapePressed()
        {
            Back();
        }
    }
}
[System.Serializable]
public class NameAvailabilityResponse
{
    public bool success;
    public bool usernameAvailable;
}