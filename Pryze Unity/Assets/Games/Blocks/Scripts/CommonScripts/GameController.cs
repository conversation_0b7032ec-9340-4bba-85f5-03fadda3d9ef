namespace Pryze.Block
{
    using System.Collections.Generic;
    using System.Linq;
    using UnityEngine;
    using Pryze.Managers;
    using Pryze.UI;
    using System;

    public class GameController : MonoBehaviour
    {
        public InGameUIController InGameUIController => DependencyManager.instance.InGameUIController;
        public static GameController instance;
    	public Canvas UICanvas;
    	public GamePreset GamePreset;
    	public int presetId;
    
    	/// <summary>
    	/// The game document.
    	/// GameDoc :- xml for BlockDetails if Left Game InBetween Gaameplay...
    	/// </summary>
    	//public XDocument GameDoc;
    	/// <summary>
    	/// This stack manages all the screen. any screen on the screen is pused and removing screen will be popped.
    	/// You cab always ask for the help if you're having trouble in changing flow.
    	/// </summary>
    	public Stack<GameObject> WindowStack = new Stack<GameObject> ();
    
    	/// <summary>
    	/// isHelpRunning Is To mention that help is running on screen or not
    	/// isHelpRunning = 0 : Means No Help isRunning
    	/// isHelpRunning = 1 : Means Help isRunning for Mode Classic
    	/// isHelpRunning = 2 : Means Help isRunning for Mode Bomb
    	/// isHelpRunning = 3 : Means Help isRunning for Mode Plus
    	/// </summary>
    	public int isHelpRunning = 0;
    
    	/// <summary>
    	/// Wheather to apply data from which game was left or not
    	/// </summary>
    	public bool PlayFromLastStatus = false;
    
    	/// <summary>
    	/// Awake this instance.
    	/// </summary>
    	void Awake ()
    	{
    		if (instance == null) {
    			instance = this;
    			return;
    		}
    		//PlayerPrefs.DeleteAll()
    		Destroy (gameObject);
    
    
    	}
    

    	public void OnEnable()
    	{
            if (!PlayerPrefs.HasKey("totalScore"))
                PlayerPrefs.SetInt("totalScore", 0);
    
            if (!PlayerPrefs.HasKey("timerValue"))
                PlayerPrefs.SetFloat("timerValue", 0f);
    
            if (!PlayerPrefs.HasKey("currentMode"))
                PlayerPrefs.SetInt("currentMode", 0);
    
            if (!PlayerPrefs.HasKey("suggestedObject1"))
                PlayerPrefs.SetString("suggestedObject1", "");
    
            if (!PlayerPrefs.HasKey("suggestedObject2"))
                PlayerPrefs.SetString("suggestedObject2", "");
    
            if (!PlayerPrefs.HasKey("suggestedObject3"))
                PlayerPrefs.SetString("suggestedObject3", "");
    
            PlayerPrefs.Save();
        }

    	public void ResetGameData()
    	{
    		// Reset PlayerPrefs keys to default empty or zero values
    		PlayerPrefs.SetInt("totalScore", 0);
    		PlayerPrefs.SetFloat("timerValue", 0f);
    		PlayerPrefs.SetInt("currentMode", 0);
    		PlayerPrefs.SetString("suggestedObject1", "");
    		PlayerPrefs.SetString("suggestedObject2", "");
    		PlayerPrefs.SetString("suggestedObject3", "");
    
    		// Save changes to disk
    		PlayerPrefs.Save();
    	}
    
    	void Start()
    	{
    		Invoke ("CheckForLastStatus",0.2f);
    		//UM_GameServiceManager.instance.Connect ();
    		Application.targetFrameRate = 60;

            int temp = Convert.ToInt32(InGameUIController.selectedTournamentData.tournamentData.game_configuration[0]);// GamePreset.presetLists[Random.Range(0, GamePreset.presetLists.Count())].presetId;
            Debug.Log("preset id " + temp);
            presetId = temp;
    
        }

    	void CheckForLastStatus()
    	{
    		
    			PlayerPrefs.DeleteKey("totalScore");
    			PlayerPrefs.DeleteKey("timerValue");
    			PlayerPrefs.DeleteKey("currentMode");
    			PlayerPrefs.DeleteKey("suggestedObject1");
    			PlayerPrefs.DeleteKey("suggestedObject2");
    			PlayerPrefs.DeleteKey("suggestedObject3");
    
    	}
    
    
    	
    	public void SetBomb(int rowId, int columnId, int bomb)
    	{
    		// Construct unique PlayerPrefs key for this bomb based on row and column
    		string key = $"bomb_{rowId}_{columnId}";
    
    		// Save/update bomb number for this position
    		PlayerPrefs.SetInt(key, bomb);
    
    		PlayerPrefs.Save();
    	}
    
    	public int GetBomb(int rowId, int columnId)
    	{
    		string key = $"bomb_{rowId}_{columnId}";
    
    		// Return bomb number if exists, else 0 (or any default)
    		return PlayerPrefs.GetInt(key, 0);
    	}
    
    	public void SetBlockColor(int rowId, int columnId, Color blockColor)
    	{
    		string baseKey = $"block_{rowId}_{columnId}_color_";
    
    		PlayerPrefs.SetFloat(baseKey + "r", blockColor.r);
    		PlayerPrefs.SetFloat(baseKey + "g", blockColor.g);
    		PlayerPrefs.SetFloat(baseKey + "b", blockColor.b);
    
    		PlayerPrefs.Save();
    	}
    
    	public Color GetBlockColor(int rowId, int columnId)
    	{
    		string baseKey = $"block_{rowId}_{columnId}_color_";
    
    		float r = PlayerPrefs.GetFloat(baseKey + "r", 0f);
    		float g = PlayerPrefs.GetFloat(baseKey + "g", 0f);
    		float b = PlayerPrefs.GetFloat(baseKey + "b", 0f);
    
    		return new Color(r, g, b);
    	}
    
    
    
    	/// <summary>
    	/// Removes the bomb node From Xml.
    	/// </summary>
    	/// <param name="rowId">Row identifier.</param>
    	/// <param name="columnId">Column identifier.</param>

    
    	public void RemoveBomb(int rowId, int columnId)
    	{
    		string key = $"bomb_{rowId}_{columnId}";
    
    		if (PlayerPrefs.HasKey(key))
    		{
    			PlayerPrefs.DeleteKey(key);
    			PlayerPrefs.Save();
    		}
    	}
    
    	void OnApplicationFocus(bool Focus)
    	{
    		if (!Focus && WindowStack.Count > 0)
    		{
    			GameObject PeekWindow = WindowStack.Peek();
    
    			if (GamePlay.instance != null && PeekWindow != null &&
    				(PeekWindow.name == "GamePlay" ||
    				 PeekWindow.name == "GamePlay_hex" ||
    				 PeekWindow.name == "Settings-Screen-GamePlay" ||
    				 PeekWindow.name == "Quit-Confirm-Play"))
    			{
    				// Save individual PlayerPrefs keys instead of XML
    				PlayerPrefs.SetInt("totalScore", GamePlay.instance.Score);
    
    				PlayerPrefs.SetInt("currentMode", (int)GamePlay.GamePlayMode);
    
    				float percentageValue = (GamePlay.instance.Timer.sizeDelta.x * 100f) / 555f;
    				int timer = (int)((percentageValue * 120) / 100f);
    				PlayerPrefs.SetInt("timerValue", timer);
    
    				PlayerPrefs.Save();
    			}
    			else
    			{
    				// Clear saved game data keys if not in gameplay window
    				PlayerPrefs.DeleteKey("totalScore");
    				PlayerPrefs.DeleteKey("currentMode");
    				PlayerPrefs.DeleteKey("timerValue");
    				PlayerPrefs.DeleteKey("suggestedObject1");
    				PlayerPrefs.DeleteKey("suggestedObject2");
    				PlayerPrefs.DeleteKey("suggestedObject3");
    
    				PlayerPrefs.Save();
    			}
    		}
    	}
    
    
    
    
    	/// <summary>
    	/// Spawns the prefab from resources.
    	/// </summary>
    	/// <returns>The prefab from resources.</returns>
    	/// <param name="path">Path.</param>
    	public GameObject SpawnPrefabFromResources (string path)
    	{
    		GameObject thisObject = (GameObject)Instantiate (Resources.Load (path));
    		thisObject.name = thisObject.name.Replace ("(Clone)", "");
    		return thisObject;
    	}
    
    	/// <summary>
    	/// Spawns the user interface screen.
    	/// </summary>
    	/// <returns>The user interface screen.</returns>
    	/// <param name="name">Name.</param>
    	/// <param name="doAddToStack">If set to <c>true</c> do add to stack.</param>
    	public GameObject SpawnUIScreen (string name, bool doAddToStack = true)
    	{
    		if (name == "GamePlay" || name == "GamePlay_help" || name == "GamePlay_hex") {
    			if(WindowStack.Count > 0) {
    				Destroy (WindowStack.Pop ());
    			}
    		}
    		GameObject thisScreen = (GameObject)Instantiate (Resources.Load ("UIScreens/" + name.ToString ()));
    		thisScreen.name = name;
    		thisScreen.transform.SetParent (UICanvas.transform);
    		thisScreen.transform.localPosition = Vector3.zero;
    		thisScreen.transform.localScale = Vector3.one;
    		thisScreen.GetComponent<RectTransform> ().sizeDelta = Vector3.zero;
    		thisScreen.Init ();
    		thisScreen.OnWindowLoad ();
    		thisScreen.SetActive (true);
    
    		if (doAddToStack) {
    			WindowStack.Push (thisScreen);
    		}
    		return thisScreen;
    	}
    
    	/// <summary>
    	/// Spawns the prefab.
    	/// </summary>
    	/// <returns>The prefab.</returns>
    	/// <param name="name">Name.</param>
    	/// <param name="doAddToStack">If set to <c>true</c> do add to stack.</param>
    	public GameObject SpawnPrefab (string name, bool doAddToStack = false)
    	{
    		GameObject thisScreen = (GameObject)Instantiate (Resources.Load ("Prefabs/GamePlay/" + name.ToString ()));
    		if (doAddToStack) {
    			WindowStack.Push (thisScreen);
    		}
    		return thisScreen;
    	}
    
    	/// <summary>
    	/// Pushes the window to stack when it is spawed.
    	/// </summary>
    	/// <param name="window">Window.</param>
    	public void PushWindow (GameObject window)
    	{
    		if (!WindowStack.Contains (window)) {
    			WindowStack.Push (window);
    		}
    	}
    
    	/// <summary>
    	/// Pops the window when it is removed.
    	/// </summary>
    	/// <returns>The window.</returns>
    	public GameObject PopWindow ()
    	{
    		if (WindowStack.Count > 0) {
    			Debug.LogError (WindowStack.Peek ().name + "  pop");
    			return WindowStack.Pop ();
    		}
    		return null;
    	}
    
    	/// <summary>
    	/// Peeks the last entered windows from the stack.
    	/// </summary>
    	/// <returns>The window.</returns>
    	public GameObject PeekWindow ()
    	{
    		if (WindowStack.Count > 0) {
    			return WindowStack.Peek ();
    		}
    		return null;
    	}
    
    	/// <summary>
    	/// Raises the back button pressed event.
    	/// </summary>
    	public void OnBackButtonPressed ()
    	{
    		if (WindowStack != null && WindowStack.Count > 0) {
    			GameObject currentWindow = WindowStack.Peek ();
    	
    			///if back button pressed from main screen, it will ask for quit-confirm.
    			if (currentWindow.name == "MainScreen") {
    				GameController.instance.SpawnUIScreen ("Quit-Confirm-Game", true);
    				return;
    			} 
    
    			/// if back button pressed during gameplay, this will ask for confirmation to quit the play.
    			else if (currentWindow.name == "GamePlay") {
    				GameController.instance.SpawnUIScreen ("Quit-Confirm-Play", true);
    				return;
    			}
    
    			///if Game Over screen is opened and back/close/home button is pressed, it will navigate to main screen.
    			else if (currentWindow.name == "GameOver") {
    				if (currentWindow.OnWindowRemove () == false) {
    					Destroy (currentWindow);
    				}
    				WindowStack.Pop ();
    				Destroy (GameController.instance.WindowStack.Pop ());
    				SpawnUIScreen("MainScreen",true);
    				return;
    			}
    
    			/// if setting screen is opened, pressing back button or close button will force screen to close.
    			else if (currentWindow.name == "Settings-Screen-Main" || currentWindow.name == "Settings-Screen-GamePlay") {
    				currentWindow.GetComponent<Settings> ().CloseMenu ();
    			} 
    
    			/// if any other screen mentioned above is opened and back button is pressed, this will lead to close that screen only.
    			else {
    				if (currentWindow.OnWindowRemove () == false) {
    					Destroy (currentWindow);
    				}
    			}
    			WindowStack.Pop ();
    		} 
    
    		InputManager.instance.DisableTouchForDelay ();
    	}
    
    	/// <summary>
    	/// Restarts the game play.
    	/// This is an adjustment made where only game
    	/// </summary>
    	public void RestartGamePlay()
    	{
    		GameObject currentWindow = WindowStack.Peek ();
    		if (currentWindow != null) {
    			if (currentWindow.name == "GameOver") {
    				if (currentWindow.OnWindowRemove () == false) {
    					Destroy (currentWindow);
    				}
    			}
    			WindowStack.Pop ();
    		}
    	}
    
    	/// <summary>
    	/// Raises the close button pressed event.
    	/// </summary>
    	public void OnCloseButtonPressed ()
    	{
    		OnBackButtonPressed ();
    	}
    
    	/// <summary>
    	/// Update this instance.
    	/// </summary>
    	//void Update ()
    	//{
    	//	///Detects the back button press event.
    	//	if (Input.GetKeyDown (KeyCode.Escape)) {
    	//		if (InputManager.instance.canInput ()) {
    	//			OnBackButtonPressed ();
    	//		}
    	//	}
    	//}
    }
}