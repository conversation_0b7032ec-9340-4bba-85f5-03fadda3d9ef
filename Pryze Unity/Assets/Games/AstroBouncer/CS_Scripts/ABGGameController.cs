﻿#if UNITY_5_3_OR_NEWER
using UnityEngine.SceneManagement;
#endif

using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using TMPro;
using DG.Tweening;
using System.Collections.Generic;
using Pryze.SpaceJumper.Settings;
using Pryze.SpaceJumper.Manager;
using System.Linq;
using Pryze.UI;
using Pryze.Managers;

using GUPS.AntiCheat.Protected;
//using System;

/// <summary>
/// This script controls the game, starting it, following game progress, and finishing it with game over. It also spawn items and enemies and shows/hides enemies
/// from the wall. When the game levels up, it increases speed and chance of enemy appearnace from the wall.
/// </summary>
public class ABGGameController : MonoBehaviour
{
	InGameUIController InGameUIController => DependencyManager.instance.InGameUIController;
	AddressableManager AddressableManager => DependencyManager.instance.AddressableManager;
	public static ABGGameController instance;
	//A list of player objects, and the number of the current player
	public ConfigrationManager ConfigrationManager;
	public GameOverScoreSetting GameOverScoreSetting;
	public Transform[] playerObjects;
	public int currentPlayer = 0;

	//The jump button of the player. This is defined in Input Manager
	public string jumpButton = "Jump";

	//Should the jump button also set the direction of the player?
	public bool jumpWithDirection = false;

	//The area within which the player can jump
	public Rect moveArea = new Rect(-8.5f, -4, 8.5f, 4);

	//Should the player wrap around the move area instead of bouncing off walls
	public bool wrapAroundMoveArea = false;

	//A list of the objects that can be dropped
	public ObjectDrop[] objectDrops;
	internal Transform[] objectDropList;

	[System.Serializable]
	public class ObjectDrop
	{
		//The object that can be dropped
		public Transform droppedObject;

		//The drop chance of the object
		public int dropChance = 1;
	}

	//The area in which objects can be dropped
	public Rect objectDropArea = new Rect(-5, -6, 5, 7);

	//How many seconds to wait before dropping another object
	public float objectDropRate = 1;
	internal float objectDropRateCount = 0;

	//The walls which hold enemies within them
	public Transform rightWall;
	public Transform leftWall;

	//The score and score text of the player
	public ProtectedInt32 GameImage = 0;

	public ProtectedInt32 score = 0;
	public Transform scoreText;
	internal int highScore = 0;

	//The overall game speed, and how much it increases with each level up
	public float gameSpeed = 1;
	public float gameSpeedIncrease = 0.1f;

	//The chance of a spike enemy showing from the wall, and how much it increases with each level up
	public float spikeChance = 0.1f;
	public float spikeChanceIncrease = 0.05f;

	//How many points the player needs to collect before leveling up
	public int levelUpEvery = 30;
	internal int increaseCount = 0;

	//Various canvases for the UI
	public Transform gameCanvas;

	//Is the game over?
	internal bool isGameOver = false;

	//The level of the main menu that can be loaded after the game ends
	public string mainMenuLevelName = "StartMenu";

	//Various sounds
	public AudioClip soundLevelUp;
	public AudioClip soundGameOver;

	//The tag of the sound source
	public string soundSourceTag = "GameController";

	//The button that pauses the game. Clicking on the pause button in the UI also pauses the game
	public string pauseButton = "Cancel";
	internal bool isPaused = false;

	//Did we start the game?
	internal bool gameStart = true;

	internal int index = 0;

	private bool isTimeOver = false;

	public Image BlackImage, purpleImage;
	public Canvas LoadingCanvas;

	//public Button TaptoStart;
	public void Awake()
	{
		instance = this;
		//--------------------------------------------
		//Screen.sleepTimeout = SleepTimeout.NeverSleep;
		//--------------------------------------------
		//UI_ManagerPryze.isLandscape = true;
		//--------------------------------------------
		Screen.orientation = ScreenOrientation.LandscapeLeft;

		GameOverScoreSetting = AddressableManager.gameOverScoreSetting;

		Pause();
	}
	// Use this for initialization
	void Start()
	{
		InGameUIController.UpdateScoreAndTime(GameImage, "Game_Start");
		//ConfigrationManager.SetDataFromAPI();
		GameOverScoreSetting.scoreData.Clear();
		StartTournamentGame();
		//Game_Timer.Instance.TimerText.fontSize = 32;
		//Game_Timer.Instance.TimerText.transform.position = new Vector3(32, Game_Timer.Instance.TimerText.transform.position.y, Game_Timer.Instance.TimerText.transform.position.z);
		Onstart();

		//if (GameManagerPryze.Instance.isFreePlay)
		//{
		//	Debug.Log("is Free Game ");
		//	EndGame_UI.instance.tapToStartLandscape.onClick.RemoveAllListeners();
		//	EndGame_UI.instance.tapToStartLandscape.onClick.AddListener(Unpause);
		//}
		InGameUIController.OnTimeComplete += GameTimeComplete;
		InGameUIController.OnGameOver += ShowGameOverScoreBoardData;

		InGameUIController.OnGamePause -= stopGameOnPause;
		InGameUIController.OnGamePause += stopGameOnPause;

		InGameUIController.OnGameContinue -= GameContinue;
		InGameUIController.OnGameContinue += GameContinue;
		InGameUIController.SetGameSoundAndHaptic?.Invoke();

		InGameUIController.tapToStart.gameObject.SetActive(true);
		InGameUIController.tapToRestart.gameObject.SetActive(false);

		InGameUIController.tapToStart.onClick.RemoveAllListeners();
		InGameUIController.tapToStart.onClick.RemoveAllListeners();
		InGameUIController.tapToStart.onClick.AddListener(() =>
		{
			Unpause();
			InGameUIController.tapToStart.gameObject.SetActive(false);

		});

		//Unpause();
	}
	public void stopGameOnPause()
	{
		//Pause();
		InGameUIController.StopTime();
		//for (int i = 0; i < dropObjectList.Count; i++)
		//{
		//	if (dropObjectList[i] != null)
		//	{
		//		dropObjectList[i].gameObject.SetActive(false);
		//	}
		//}
		Time.timeScale = 0;
	}

	public void GameContinue()
	{
		//for (int i = 0; i < dropObjectList.Count; i++)
		//{
  //          if (dropObjectList[i] != null)
  //          {

		//	dropObjectList[i].gameObject.SetActive(true);
  //          }
		//}
		//Unpause();
		InGameUIController.StartTime();
		//dropObjectList.Clear();
		Time.timeScale = 1;
	}
	public void GameTimeComplete()
	{
		//for (int i = 0; i < dropObjectList.Count; i++)
		//{
		//	if (dropObjectList[i] != null)
		//	{
		//		dropObjectList[i].gameObject.SetActive(false);
		//	}
		//}
		InGameUIController.UpdateScoreAndTime(GameImage, "Game_End");
		//Game_Timer.Instance.timeOvertTxt.fontSize = 72;
		isTimeOver = true;
		LoadingCanvas.sortingOrder = 2;
		Invoke("DelayImg", 1);
		Pause();
		//Time.timeScale = 0;

		playerObjects[currentPlayer].gameObject.SetActive(false);

	}
	public void DelayImg()
	{
		//Game_Timer.Instance.timeOvertTxt.gameObject.SetActive(false);
		purpleImage.gameObject.SetActive(true);
		InGameUIController.timeOvertTxt.gameObject.SetActive(false);
		InGameUIController.InGameHeaderComponent.gameObject.SetActive(false);
	}
	private void Onstart()
	{
		InGameUIController.InGameHeaderComponent.ShowLandscapeUI();
		//Update the score without adding to it
		//ChangeScore(0);

		//Get the currently selected player from PlayerPrefs
		currentPlayer = PlayerPrefs.GetInt("CurrentPlayer", currentPlayer);

		//Set the current player object
		SetPlayer(currentPlayer);
		//Calculate the chances for the objects to drop
		int totalDrops = 0;
		int totalDropsIndex = 0;

		//Calculate the total number of drops with their chances
		for (index = 0; index < objectDrops.Length; index++)
		{
			totalDrops += objectDrops[index].dropChance;
		}

		//Create a new list of the objects that can be dropped
		objectDropList = new Transform[totalDrops];

		//Go through the list again and fill out each type of drop based on its drop chance
		for (index = 0; index < objectDrops.Length; index++)
		{
			int dropChanceCount = 0;

			while (dropChanceCount < objectDrops[index].dropChance)
			{
				objectDropList[totalDropsIndex] = objectDrops[index].droppedObject;

				dropChanceCount++;

				totalDropsIndex++;
			}
		}
		Invoke("HideLoadingCanvas", 1);
	}
	public void HideLoadingCanvas()
	{
		BlackImage.DOFade(0, 1).OnComplete(() =>
		{
			LoadingCanvas.sortingOrder = 1;
			BlackImage.gameObject.SetActive(false);
		});
	}
	public void Delay()
	{
		Screen.orientation = ScreenOrientation.Portrait;
		//Game_Timer.Instance.timeOvertTxt.fontSize = 22;
	}
	// Update is called once per frame
	void Update () 
	{
		
		if (isPaused)
        {
			if (isTimeOver)
			{
				Invoke("Delay", 1);
			}
			return;
        }
		//Assign the player object
		//if ( playerObjects[currentPlayer] == null )    playerObjects[currentPlayer] = GameObject.FindGameObjectWithTag(playerTag).transform;		
		
		//If the game is over, listen for the Restart and MainMenu buttons
		if ( isGameOver == true )
		{
			//The jump button restarts the game
			if ( Input.GetButtonDown(jumpButton) )
			{
				//Restart();
			}
			
			//The pause button goes to the main menu
			if ( Input.GetButtonDown(pauseButton) )
			{
				MainMenu();
			}
		}
		else
		{
			if ( objectDropList.Length > 0 )
			{
				//Count the drop rate of the objects
				objectDropRateCount += Time.deltaTime;

				//Drop an object	
				if ( objectDropRateCount >= objectDropRate )
				{
				//Debug.Log("objectDropRateCount " + objectDropRateCount);
					DropObject();
					
					objectDropRateCount = 0;
				}
			}
			
			//If the player presses the jump button, jump!
			//if ( Input.GetButtonDown(pauseButton) )
			//{
			//	if ( isPaused == true )    Unpause();
			//	else    Pause();
			//}
			
			if ( playerObjects[currentPlayer] )
			{
				//Jump only if we are within the move area
				if ( playerObjects[currentPlayer].position.y < moveArea.height )
				{
					//If the player jumps while the game is paused, unpause it
					if ( isPaused == true && Input.GetButtonDown(jumpButton) )
					{
						Unpause();			
					}
					else
					{
						if ( jumpWithDirection == true )
						{
							//If we press the positive jump axis, jump right
							if ( Input.GetAxisRaw(jumpButton) > 0 )
							{
								playerObjects[currentPlayer].SendMessage("Jump",1);
							}
							
							//If we press the negative jump axis, jump left
							if ( Input.GetAxisRaw(jumpButton) < 0 )
							{
								playerObjects[currentPlayer].SendMessage("Jump",-1);
							}
						}
						else
						{
							//If we press the jump button, jump
							if ( Input.GetButtonDown(jumpButton) )
							{
								playerObjects[currentPlayer].SendMessage("Jump", 0.0f);
							}
						}
					}
				}
				
				//If the player reaches the left edge of the move area, bounce off it to the right
				if ( playerObjects[currentPlayer].position.x < moveArea.x )
				{
					//Place the player at the edge of the move area, so it doesn't accidentally get stuck beyond it
					playerObjects[currentPlayer].position = new Vector3( moveArea.x, playerObjects[currentPlayer].position.y, playerObjects[currentPlayer].position.z);

					if ( wrapAroundMoveArea == true )    playerObjects[currentPlayer].position = new Vector3( moveArea.width, playerObjects[currentPlayer].position.y, playerObjects[currentPlayer].position.z);
					else    playerObjects[currentPlayer].SendMessage("BounceOffWall");
				}
				
				//If the player reaches the left edge of the move area, bounce off it to the left
				if ( playerObjects[currentPlayer].position.x > moveArea.width )
				{
					//Place the player at the edge of the move area, so it doesn't accidentally get stuck beyond it
					playerObjects[currentPlayer].position = new Vector3( moveArea.width, playerObjects[currentPlayer].position.y, playerObjects[currentPlayer].position.z);

					
					if ( wrapAroundMoveArea )    playerObjects[currentPlayer].position = new Vector3( moveArea.x, playerObjects[currentPlayer].position.y, playerObjects[currentPlayer].position.z);
					else    playerObjects[currentPlayer].SendMessage("BounceOffWall");
				}
			}
		}
	}

	public List<DropObjList> dropObjLists;
	//Drop a random object for the list of objects
	public int DropCount;
	public void DropObject()
	{
		DropCount++;
		if (!isPaused)
		{
            var DropObjID = _presetData.dropObjLists[DropCount].dropObjID;
            var a = Instantiate(objectDropList[Mathf.FloorToInt(DropObjID)], _presetData.dropObjLists[DropCount].pos, Quaternion.identity);

			dropObjectList.Add(a.gameObject);
			//}
		}
	}
	public List<GameObject> dropObjectList = new List<GameObject>();
	//This function changes the score of the player
	public void ChangeScore(ProtectedInt32 changeValue)
	{
		print("This is changeValue in ChangeScore" + changeValue);
		//Change the score
		GameImage += changeValue;

		int honeypotScore = (System.Int32)this.GameImage.GetType().GetField("fakeValue", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance).GetValue(this.GameImage);
		//Update the score text
		if (scoreText)
		{
			//scoreText.GetComponent<TMP_Text>().text = GameImage.ToString();
			InGameUIController.InGameHeaderComponent.scoreTextLS.text = GameImage.ToString();
			//InGameUIController.UpdateScoreAndTime(GameImage);
			//EndGame_UI.instance.score.text = GameImage.ToString();
		}
		//GameManagerPryze.Instance.LoadImage_ScreenOrientation = GameImage;
		//Increase the counter to the next level
		increaseCount += changeValue;
		//If we reached the required number of points, level up!
		if (increaseCount >= levelUpEvery)
		{
			increaseCount -= levelUpEvery;
			LevelUp();
		}
		if (changeValue < 0)
		{
			InGameUIController.UpdateScoreAndTime(GameImage, "Death_5");
		}
		else
		{
			Debug.Log("Here Star Call " + changeValue);
			InGameUIController.UpdateScoreAndTime(GameImage, "Star_" + changeValue);
		}
		//EndGame_UI.instance.UpdateScoreAndTime(GameImage);
	}
	
	//This function levels up, and increases the difficulty of the game
	public void LevelUp()
	{
		//Increase the chance of spikes appearing from the wall
		spikeChance += spikeChanceIncrease;
		
		//Increase the speed of the game
		gameSpeed += gameSpeedIncrease/2;
		if (gameSpeed >= 2.5f)
		{
			gameSpeed = 2;
		}
		//Set time scale to game speed
		Time.timeScale = gameSpeed;
		
		//If there is a source and a sound, play it from the source
		if ( soundSourceTag != "" && soundLevelUp )    GameObject.FindGameObjectWithTag(soundSourceTag).GetComponent<AudioSource>().PlayOneShot(soundLevelUp);
		
		//Shake the camera
		if ( GetComponent<ABGShakeCamera>() )    GetComponent<ABGShakeCamera>().StartShake();
	}
	public int rightWallHitCount, leftWallHitCount;
	public List<LeftSpikeList> LeftSpikeList;
	public List<RightSpikeList> RightSpikeList;
	//This function runs when the player hits the wall. It flips the player's direction and shows/hides some of the spikes in the walls
	public void HitWall(string wallSide)
	{
		//Hitting the left wall
		if (wallSide == "left")
		{
			if (leftWall)
			{
				leftWallHitCount++;
				//Go through all the spikes in the wall, hide some of them, and show some
				//if (GameManagerPryze.Instance.isFreePlay)
				{
					Debug.Log("Left Wall Hit in freeplay");
					LeftSpikeList leftSpikeList = new LeftSpikeList();
					leftSpikeList.WallHitID = leftWallHitCount;
					foreach (Transform spike in leftWall)
					{
						var RanValue = Random.value;
						if (RanValue > spikeChance)
						{
							spike.gameObject.SendMessage("Hide");
						}
						else
						{
							SpikeData spikeData = new SpikeData();
							spikeData.isShow = true;
							spikeData.SpikeID = spike.GetComponent<ABGEnemy>().spikeID;
							leftSpikeList.SpikeList.Add(spikeData);
							spike.gameObject.SendMessage("Show");
						}
					}
					LeftSpikeList.Add(leftSpikeList);
					//Make sure at least one of the spikes is hidden, to give the player a chance to bounce off without being hit
					leftWall.GetChild(Mathf.FloorToInt(Random.Range(0, leftWall.childCount))).gameObject.SendMessage("Hide");
				}
				//else if (GameManagerPryze.Instance.isTournament)
				//{
				//	Debug.Log("Left Wall Hit in Tournament");
				//	var a = _presetData.leftSpikeList.First(i => i.WallHitID == leftWallHitCount);
				//	for (int i = 0; i < a.SpikeList.Count; i++)
				//	{
				//		foreach (Transform spike in leftWall)
				//		{
				//			if (a.SpikeList.Count > 0)
				//			{

				//				if (spike.GetComponent<ABGEnemy>().spikeID == a.SpikeList[i].SpikeID)
				//				{
				//					spike.gameObject.SendMessage("Show");
				//				}
				//				else
				//				{
				//					spike.gameObject.SendMessage("Hide");
				//				}
				//			}
				//		}
				//	}
				//}
			}
		}
		else if (wallSide == "right")
		{
			if (rightWall)
			{
				//Go through all the spikes in the wall, hide some of them, and show some 
				rightWallHitCount++;
				//if (GameManagerPryze.Instance.isFreePlay)
				{
					Debug.Log("Right Wall Hit in freeplay");
					RightSpikeList rightSpikeList = new RightSpikeList();
					rightSpikeList.WallHitID = rightWallHitCount;
					foreach (Transform spike in rightWall)
					{
						var RanValue = Random.value;
						if (RanValue > spikeChance)
						{
							spike.gameObject.SendMessage("Hide");
						}
						else
						{
							SpikeData spikeData = new SpikeData();
							spikeData.isShow = true;
							spikeData.SpikeID = spike.GetComponent<ABGEnemy>().spikeID;
							rightSpikeList.SpikeList.Add(spikeData);
							spike.gameObject.SendMessage("Show");
						}
					}
					RightSpikeList.Add(rightSpikeList);
					//Make sure at least one of the spikes is hidden, to give the player a chance to bounce off without being hit
					rightWall.GetChild(Mathf.FloorToInt(Random.Range(0, rightWall.childCount))).gameObject.SendMessage("Hide");
				}
				//else if (GameManagerPryze.Instance.isTournament)
				//{
				//	Debug.Log("Right Wall Hit in Tournament");
				//	var a = _presetData.rightSpikeList.First(i => i.WallHitID == rightWallHitCount);

				//	for (int i = 0; i < a.SpikeList.Count; i++)
				//	{
				//		foreach (Transform spike in rightWall)
				//		{
				//			if (a.SpikeList.Count > 0)
				//			{

				//				if (spike.GetComponent<ABGEnemy>().spikeID == a.SpikeList[i].SpikeID)
				//				{
				//					spike.gameObject.SendMessage("Show");
				//				}
				//				else
				//				{
				//					spike.gameObject.SendMessage("Hide");
				//				}
				//			}
				//		}
				//	}
				//}
			}
		}
	}
	//This function pauses the game
	public void Pause()
	{
		Debug.Log("Pause ");
		isPaused = true;
		playerObjects[currentPlayer].GetComponent<Rigidbody2D>().constraints = RigidbodyConstraints2D.FreezeAll;
		//Set timescale to 0, preventing anything from moving
		//Time.timeScale = 0;		
		//if ( gameCanvas )    gameCanvas.gameObject.SetActive(false);
    }
	
	public void Unpause()
	{
		InGameUIController.StartTime();
		//EndGame_UI.instance.tapToStartLandscape.gameObject.SetActive(false);
		isPaused = false;
		
		//Set timescale back to the current game speed
		Time.timeScale = gameSpeed;		
		//Hide the pause screen and show the game screen		
		if ( gameCanvas )    gameCanvas.gameObject.SetActive(true);		
		//if we are at the start of the game, make the player jump
		if ( playerObjects[currentPlayer] && GameImage == 0 && gameStart == true )    
		{
			playerObjects[currentPlayer].SendMessage("Jump", 1);
			
			gameStart = false;		
		}
	}
	
	//This function triggers a jump function on the player object associated with this gamecontroller
	//It is used to bypass having to reassign the player object on the jump buttons in the 4.6 UI
	public void PlayerJump( int jumpDirection )
	{
		if ( playerObjects[currentPlayer] )    
		{
			if ( playerObjects[currentPlayer].position.y < moveArea.height )    playerObjects[currentPlayer].SendMessage("Jump", jumpDirection);
		}
	}
	
	//This function handles the game over event
	IEnumerator GameOver()
	{
		if ( isGameOver == false )
		{
			yield return new WaitForSeconds(1);

			isGameOver = true;
			
			//If there is a source and a sound, play it from the source
			if ( soundSourceTag != "" && soundGameOver )    GameObject.FindGameObjectWithTag(soundSourceTag).GetComponent<AudioSource>().PlayOneShot(soundGameOver);
			
		
			if ( gameCanvas )    Destroy(gameCanvas.gameObject);
			{ 
				//Check if we got a high score
				if (GameImage > highScore )    
				{
					highScore = GameImage;
					//Register the new high score
					#if UNITY_5_3_OR_NEWER
					PlayerPrefs.SetInt(SceneManager.GetActiveScene().name + "_HighScore", GameImage);
					#else
					PlayerPrefs.SetInt(Application.loadedLevelName + "_HighScore", score);
					#endif
				}
				
			
			}
			
			//Reset the global score
			//ABGGameController.score = 0;
			
			Time.timeScale = 0;
		}
	}
	
	//This function handles the victory event
	IEnumerator Victory()
	{
		if ( isGameOver == false )
		{
			yield return new WaitForSeconds(1);
			isGameOver = true;			
			//If there is a source and a sound, play it from the source
			if ( soundSourceTag != "" && soundGameOver )    GameObject.FindGameObjectWithTag(soundSourceTag).GetComponent<AudioSource>().PlayOneShot(soundGameOver);
			if ( gameCanvas )    Destroy(gameCanvas.gameObject);
			//Reset the global score
			GameImage = 0;
			Time.timeScale = 0;
		}
	}
	
	
	
	//This function returns to the Main Menu
	public void MainMenu()
	{
		Time.timeScale = 1;
		#if UNITY_5_3_OR_NEWER
		SceneManager.LoadScene(mainMenuLevelName);
		#else
		Application.LoadLevel(mainMenuLevelName);
		#endif
	}
	
	//This function activates the selected player, while deactivating all the others
	public void SetPlayer( int playerNumber )
	{
		//Go through all the players, and hide each one except the current player
		for( index = 0; index < playerObjects.Length; index++ )
		{
			if ( index != playerNumber )    playerObjects[index].gameObject.SetActive(false);
			else    playerObjects[index].gameObject.SetActive(true);
		}
	}
	
	//This function draws the object spawn area in the editor
	public void OnDrawGizmosSelected()
	{
		Gizmos.color = Color.red;
		
		Gizmos.DrawLine(new Vector3(objectDropArea.x,objectDropArea.y,0), new Vector3(objectDropArea.width,objectDropArea.y,0));
		Gizmos.DrawLine(new Vector3(objectDropArea.x,objectDropArea.height,0), new Vector3(objectDropArea.width,objectDropArea.height,0));
		Gizmos.DrawLine(new Vector3(objectDropArea.x,objectDropArea.y,0), new Vector3(objectDropArea.x,objectDropArea.height,0));
		Gizmos.DrawLine(new Vector3(objectDropArea.width,objectDropArea.y,0), new Vector3(objectDropArea.width,objectDropArea.height,0));
		
		//Draw the player's movements area
		Gizmos.color = Color.green;
		
		Gizmos.DrawLine(new Vector3(moveArea.x,moveArea.y,0), new Vector3(moveArea.width,moveArea.y,0));
		Gizmos.DrawLine(new Vector3(moveArea.x,moveArea.height,0), new Vector3(moveArea.width,moveArea.height,0));
		Gizmos.DrawLine(new Vector3(moveArea.x,moveArea.y,0), new Vector3(moveArea.x,moveArea.height,0));
		Gizmos.DrawLine(new Vector3(moveArea.width,moveArea.y,0), new Vector3(moveArea.width,moveArea.height,0));
	}
	public void ShowGameOverScoreBoardData()
	{
		InGameUIController.isGamePause = true;
		int honeypotScore = (System.Int32)this.GameImage.GetType().GetField("fakeValue", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance).GetValue(this.GameImage);
		GameOverScoreSetting.HighScore = GameImage;
		AddScoreToScoreBoard("Base Score", GameImage.ToString());
		//AddScoreToScoreBoard("Star Bonus", 0);
		AddScoreToScoreBoard("Time", InGameUIController.besttime);
		InGameUIController.ShowGameOverScreen();

		Invoke("hidePurpleScreen", 3f);

		

	}

	public void hidePurpleScreen()
	{
		Debug.Log("hide p img");
		purpleImage.gameObject.SetActive(false);
	}


	private void AddScoreToScoreBoard(string id, string score)
	{
		ScoreItem scoreItem = new ScoreItem();
		scoreItem.ItemName = id;
		scoreItem.ItemValue = score.ToString();
		GameOverScoreSetting.scoreData.Add(scoreItem);
	}
	public PresetData _presetData;
	public void StartTournamentGame()
	{
		var PresetID = InGameUIController.selectedTournamentData.tournamentData.game_configuration[0];
		_presetData = ConfigrationManager.ConfigrationSetting.configrationSettingData.PresetData.First(i => i.PresetID == PresetID.ToString());
	}
}




