using UnityEngine;
using Pryze.Managers;
using Pryze.UI;
namespace Pryze.SpaceJumper
{
    public class SpaceJumperTutorialManager : TutorialBase
    {
        public static SpaceJumperTutorialManager instance;
        [SerializeField]ABGGameController ABGGameController;

        private void Awake()
        {
            instance = this;
        }
        void Start()
        {
            isTutorial = !TutorialManager.TutorialSettingData.gamesTutorialConfigrationList.isSpaceJumperTutorialComplete;
            if (isTutorial)
            {
                HowToPlayStart();
                OnPlay -= startGame;
                OnPlay += startGame;
            }
        }
        public void startGame()
        {
            TutorialManager.TutorialSettingData.gamesTutorialConfigrationList.isSpaceJumperTutorialComplete = true;
            TutorialManager.SaveUserData();
            ABGGameController.Unpause();
        }

        //public void HowToPlayStart()
        //{
        //    InGameUIController.inGameTutorialScreen.gameObject.SetActive(true);
        //    InGameUIController.inGameTutorialScreen.OnTutorialComplete -= PlayGame;
        //    InGameUIController.inGameTutorialScreen.OnTutorialComplete += PlayGame;
        //}
        //public void PlayGame()
        //{
        //    InGameUIController.inGameTutorialScreen.gameObject.SetActive(false);
        //    TutorialManager.TutorialSettingData.gamesTutorialConfigrationList.isSpaceJumperTutorialComplete = true;
        //    TutorialManager.SaveUserData();
        //}
    }
}


