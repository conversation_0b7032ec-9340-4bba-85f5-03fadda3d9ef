                        -H/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=27
-DANDROID_PLATFORM=android-27
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK
-DCMAKE_ANDROID_NDK=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK
-DCMAKE_TOOLCHAIN_FILE=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/prefab/armeabi-v7a/prefab
-B/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2