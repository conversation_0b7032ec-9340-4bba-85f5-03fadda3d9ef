# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Unity
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a" && /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp" -B"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a" && /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp" -B"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target game


#############################################
# Order-only phony target for game

build cmake_object_order_depends_target_game: phony || GameActivity/CMakeFiles/game.dir

build GameActivity/CMakeFiles/game.dir/UGAApplication.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGAApplication.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"

build GameActivity/CMakeFiles/game.dir/UGAConfiguration.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGAConfiguration.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"

build GameActivity/CMakeFiles/game.dir/UGADebug.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGADebug.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"

build GameActivity/CMakeFiles/game.dir/UGAEntry.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEntry.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGAEntry.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"

build GameActivity/CMakeFiles/game.dir/UGAInput.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGAInput.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"

build GameActivity/CMakeFiles/game.dir/UGAInputKeyEvent.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputKeyEvent.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGAInputKeyEvent.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"

build GameActivity/CMakeFiles/game.dir/UGAInputMotionEvent.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputMotionEvent.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGAInputMotionEvent.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"

build GameActivity/CMakeFiles/game.dir/UGASoftKeyboard.cpp.o: CXX_COMPILER__game_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity/CMakeFiles/game.dir/UGASoftKeyboard.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  OBJECT_FILE_DIR = GameActivity/CMakeFiles/game.dir
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target game


#############################################
# Link the shared library /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.so

build /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.so: CXX_SHARED_LIBRARY_LINKER__game_RelWithDebInfo GameActivity/CMakeFiles/game.dir/UGAApplication.cpp.o GameActivity/CMakeFiles/game.dir/UGAConfiguration.cpp.o GameActivity/CMakeFiles/game.dir/UGADebug.cpp.o GameActivity/CMakeFiles/game.dir/UGAEntry.cpp.o GameActivity/CMakeFiles/game.dir/UGAInput.cpp.o GameActivity/CMakeFiles/game.dir/UGAInputKeyEvent.cpp.o GameActivity/CMakeFiles/game.dir/UGAInputMotionEvent.cpp.o GameActivity/CMakeFiles/game.dir/UGASoftKeyboard.cpp.o | /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/libs/android.armeabi-v7a/libgame-activity_static.a
  LANGUAGE_COMPILE_FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Qunused-arguments -Wl,--no-undefined
  LINK_LIBRARIES = -landroid  /Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/libs/android.armeabi-v7a/libgame-activity_static.a  -llog   -latomic -lm
  OBJECT_DIR = GameActivity/CMakeFiles/game.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libgame.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = GameActivity/CMakeFiles/game.dir/
  TARGET_FILE = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.so"
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.pdb"


#############################################
# Utility command for edit_cache

build GameActivity/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a/GameActivity" && /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp" -B"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build GameActivity/edit_cache: phony GameActivity/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build GameActivity/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a/GameActivity" && /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp" -B"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build GameActivity/rebuild_cache: phony GameActivity/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target swappywrapper


#############################################
# Order-only phony target for swappywrapper

build cmake_object_order_depends_target_swappywrapper: phony || FramePacing/CMakeFiles/swappywrapper.dir

build FramePacing/CMakeFiles/swappywrapper.dir/UnitySwappyWrapper.cpp.o: CXX_COMPILER__swappywrapper_RelWithDebInfo /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/UnitySwappyWrapper.cpp || cmake_object_order_depends_target_swappywrapper
  DEFINES = -DEXTERNAL_FRAME_PACING_CODE -Dswappywrapper_EXPORTS
  DEP_FILE = FramePacing/CMakeFiles/swappywrapper.dir/UnitySwappyWrapper.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC
  INCLUDES = -I"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing" -isystem /Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/include
  OBJECT_DIR = FramePacing/CMakeFiles/swappywrapper.dir
  OBJECT_FILE_DIR = FramePacing/CMakeFiles/swappywrapper.dir
  TARGET_COMPILE_PDB = FramePacing/CMakeFiles/swappywrapper.dir/
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target swappywrapper


#############################################
# Link the shared library /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.so

build /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.so: CXX_SHARED_LIBRARY_LINKER__swappywrapper_RelWithDebInfo FramePacing/CMakeFiles/swappywrapper.dir/UnitySwappyWrapper.cpp.o | /Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/libs/android.armeabi-v7a_API27_NDK23_cpp_shared_Release/libswappy.a
  LANGUAGE_COMPILE_FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Qunused-arguments -Wl,--no-undefined -Wl,--wrap=__android_log_print
  LINK_LIBRARIES = -landroid  /Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/libs/android.armeabi-v7a_API27_NDK23_cpp_shared_Release/libswappy.a  -llog   -latomic -lm
  OBJECT_DIR = FramePacing/CMakeFiles/swappywrapper.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libswappywrapper.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = FramePacing/CMakeFiles/swappywrapper.dir/
  TARGET_FILE = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.so"
  TARGET_PDB = "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.pdb"


#############################################
# Utility command for edit_cache

build FramePacing/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a/FramePacing" && /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp" -B"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build FramePacing/edit_cache: phony FramePacing/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build FramePacing/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a/FramePacing" && /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp" -B"/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build FramePacing/rebuild_cache: phony FramePacing/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build game: phony /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.so

build libgame.so: phony /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.so

build libswappywrapper.so: phony /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.so

build swappywrapper: phony /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a

build all: phony GameActivity/all FramePacing/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a/FramePacing

build FramePacing/all: phony /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libswappywrapper.so

# =============================================================================

#############################################
# Folder: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/armeabi-v7a/GameActivity

build GameActivity/all: phony /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/2a3k5q69/obj/armeabi-v7a/libgame.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/abis.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/adjust_api_level.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/flags.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android-Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android-Determine.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Determine-Compiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Determine.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Initialize.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Determine-Compiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/platforms.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Common.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler-NDK.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/.utmp/RelWithDebInfo/2a3k5q69/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/game-activity/game-activityConfig.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/.utmp/RelWithDebInfo/2a3k5q69/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/games-frame-pacing/games-frame-pacingConfig.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/.utmp/RelWithDebInfo/2a3k5q69/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/games-frame-pacing/games-frame-pacingConfigVersion.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/abis.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/adjust_api_level.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/flags.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android-Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android-Determine.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Determine-Compiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Determine.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Initialize.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Determine-Compiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/platforms.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Common.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler-NDK.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/.utmp/RelWithDebInfo/2a3k5q69/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/game-activity/game-activityConfig.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/.utmp/RelWithDebInfo/2a3k5q69/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/games-frame-pacing/games-frame-pacingConfig.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/.utmp/RelWithDebInfo/2a3k5q69/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/games-frame-pacing/games-frame-pacingConfigVersion.cmake /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt /Users/<USER>/Desktop/Git/PryzePro/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
