The target system is: Android - 27 - aarch64
The host system is: Darwin - 24.5.0 - x86_64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang 
Build flags: 
Id flags: -c;--target=aarch64-none-linux-android27 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ 
Build flags: 
Id flags: -c;--target=aarch64-none-linux-android27 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CompilerIdCXX/CMakeCXXCompilerId.o"

Detecting C compiler ABI info compiled with the following output:
Change Dir: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja cmTC_47b11 && [1/2] Building C object CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o
Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android27
Thread model: posix
InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin
Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 (in-process)
 "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang" -cc1 -triple aarch64-none-linux-android27 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -target-linker-version 650.9 -v -ffunction-sections -fdata-sections -resource-dir /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8 -dependency-file CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include -O2 -Wformat -fdebug-compilation-dir "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o -x c /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c
clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-apple-darwin24.5.0
ignoring nonexistent directory "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include"
#include "..." search starts here:
#include <...> search starts here:
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include
End of search list.
[2/2] Linking C executable cmTC_47b11
Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android27
Thread model: posix
InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin
Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/ld" --sysroot=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_47b11 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o -latomic -lm /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include]
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include]
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include]
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include]
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]
  implicit include dirs: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja cmTC_47b11 && [1/2] Building C object CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android27]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin]
  ignore line: [Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang" -cc1 -triple aarch64-none-linux-android27 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -target-linker-version 650.9 -v -ffunction-sections -fdata-sections -resource-dir /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8 -dependency-file CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include -O2 -Wformat -fdebug-compilation-dir "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o -x c /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-apple-darwin24.5.0]
  ignore line: [ignoring nonexistent directory "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_47b11]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android27]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin]
  ignore line: [Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/ld" --sysroot=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_47b11 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o -latomic -lm /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/ld] ==> ignore
    arg [--sysroot=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_47b11] ==> ignore
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o] ==> obj [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_47b11.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-latomic] ==> lib [atomic]
    arg [-lm] ==> lib [m]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o] ==> obj [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o]
  remove lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib]
  implicit libs: [atomic;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o]
  implicit dirs: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib/gcc/aarch64-linux-android/4.9.x;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja cmTC_06fb6 && [1/2] Building CXX object CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o
Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android27
Thread model: posix
InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin
Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 (in-process)
 "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -cc1 -triple aarch64-none-linux-android27 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -target-linker-version 650.9 -v -ffunction-sections -fdata-sections -resource-dir /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8 -dependency-file CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1 -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include -O2 -Wformat -fdeprecated-macro -fdebug-compilation-dir "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o -x c++ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp
clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-apple-darwin24.5.0
ignoring nonexistent directory "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include"
#include "..." search starts here:
#include <...> search starts here:
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android
 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include
End of search list.
[2/2] Linking CXX executable cmTC_06fb6
Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android27
Thread model: posix
InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin
Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/ld" --sysroot=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_06fb6 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o -latomic -lm -lc++ -lm /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1]
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include]
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include]
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1]
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include]
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include]
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]
  implicit include dirs: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja cmTC_06fb6 && [1/2] Building CXX object CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android27]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin]
  ignore line: [Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -cc1 -triple aarch64-none-linux-android27 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -target-linker-version 650.9 -v -ffunction-sections -fdata-sections -resource-dir /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8 -dependency-file CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1 -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include -internal-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include -internal-externc-isystem /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include -O2 -Wformat -fdeprecated-macro -fdebug-compilation-dir "/Users/<USER>/Desktop/Git/PryzePro/Pryze Unity/.utmp/RelWithDebInfo/2a3k5q69/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o -x c++ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-apple-darwin24.5.0]
  ignore line: [ignoring nonexistent directory "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/c++/v1]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/local/include]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/include]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_06fb6]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android27]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin]
  ignore line: [Found candidate GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/ld" --sysroot=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_06fb6 /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27 -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android -L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o -latomic -lm -lc++ -lm /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/ld] ==> ignore
    arg [--sysroot=/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_06fb6] ==> ignore
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o] ==> obj [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-L/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib] ==> dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_06fb6.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-latomic] ==> lib [atomic]
    arg [-lm] ==> lib [m]
    arg [-lc++] ==> lib [c++]
    arg [-lm] ==> lib [m]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o] ==> obj [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o]
  remove lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib] ==> [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib]
  implicit libs: [atomic;m;c++;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtbegin_dynamic.o;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27/crtend_android.o]
  implicit dirs: [/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib64/clang/12.0.8/lib/linux/aarch64;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/lib/gcc/aarch64-linux-android/4.9.x;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/27;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android;/Applications/Unity/Hub/Editor/6000.0.23f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib]
  implicit fwks: []


