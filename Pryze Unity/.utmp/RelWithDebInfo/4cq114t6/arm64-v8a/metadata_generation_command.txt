                        -HD:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK
-DCMAKE_ANDROID_NDK=C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK
-DCMAKE_TOOLCHAIN_FILE=C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\RelWithDebInfo\4cq114t6\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\RelWithDebInfo\4cq114t6\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-DCMAKE_FIND_ROOT_PATH=D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\RelWithDebInfo\4cq114t6\prefab\arm64-v8a\prefab
-BD:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\RelWithDebInfo\4cq114t6\arm64-v8a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2