{"buildFiles": ["D:\\GitLab\\Pryze Unity 6\\Pryze Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "D:\\GitLab\\Pryze Unity 6\\Pryze Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\FramePacing\\CMakeLists.txt", "D:\\GitLab\\Pryze Unity 6\\Pryze Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.7f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitLab\\Pryze Unity 6\\Pryze Unity\\.utmp\\Debug\\3c5q4h5p\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.7f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitLab\\Pryze Unity 6\\Pryze Unity\\.utmp\\Debug\\3c5q4h5p\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"game::@d02bb112ea9f9c2ed29f": {"artifactName": "game", "abi": "armeabi-v7a", "output": "D:\\GitLab\\Pryze Unity 6\\Pryze Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\Debug\\3c5q4h5p\\obj\\armeabi-v7a\\libgame.so", "runtimeFiles": []}, "swappywrapper::@7e25bd1f32ce224db4e9": {"artifactName": "swappywrapper", "abi": "armeabi-v7a", "output": "D:\\GitLab\\Pryze Unity 6\\Pryze Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\Debug\\3c5q4h5p\\obj\\armeabi-v7a\\libswappywrapper.so", "runtimeFiles": []}}}