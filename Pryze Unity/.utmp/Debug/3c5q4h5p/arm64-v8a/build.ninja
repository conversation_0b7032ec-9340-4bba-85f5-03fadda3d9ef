# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Unity
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/.utmp/Debug/3c5q4h5p/arm64-v8a/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a" && "C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a" && "C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\cpp" -B"D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target game


#############################################
# Order-only phony target for game

build cmake_object_order_depends_target_game: phony || GameActivity/CMakeFiles/game.dir

build GameActivity/CMakeFiles/game.dir/UGAApplication.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGAApplication.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir

build GameActivity/CMakeFiles/game.dir/UGAConfiguration.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGAConfiguration.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir

build GameActivity/CMakeFiles/game.dir/UGADebug.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGADebug.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir

build GameActivity/CMakeFiles/game.dir/UGAEntry.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEntry.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGAEntry.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir

build GameActivity/CMakeFiles/game.dir/UGAInput.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGAInput.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir

build GameActivity/CMakeFiles/game.dir/UGAInputKeyEvent.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputKeyEvent.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGAInputKeyEvent.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir

build GameActivity/CMakeFiles/game.dir/UGAInputMotionEvent.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputMotionEvent.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGAInputMotionEvent.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir

build GameActivity/CMakeFiles/game.dir/UGASoftKeyboard.cpp.o: CXX_COMPILER__game_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.cpp || cmake_object_order_depends_target_game
  DEFINES = -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS
  DEP_FILE = GameActivity\CMakeFiles\game.dir\UGASoftKeyboard.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/include
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  OBJECT_FILE_DIR = GameActivity\CMakeFiles\game.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target game


#############################################
# Link the shared library D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\Debug\3c5q4h5p\obj\arm64-v8a\libgame.so

build D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libgame.so: CXX_SHARED_LIBRARY_LINKER__game_Debug GameActivity/CMakeFiles/game.dir/UGAApplication.cpp.o GameActivity/CMakeFiles/game.dir/UGAConfiguration.cpp.o GameActivity/CMakeFiles/game.dir/UGADebug.cpp.o GameActivity/CMakeFiles/game.dir/UGAEntry.cpp.o GameActivity/CMakeFiles/game.dir/UGAInput.cpp.o GameActivity/CMakeFiles/game.dir/UGAInputKeyEvent.cpp.o GameActivity/CMakeFiles/game.dir/UGAInputMotionEvent.cpp.o GameActivity/CMakeFiles/game.dir/UGASoftKeyboard.cpp.o | C$:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/libs/android.arm64-v8a/libgame-activity_static.a
  LANGUAGE_COMPILE_FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Qunused-arguments -Wl,--no-undefined
  LINK_LIBRARIES = -landroid  C:/Users/<USER>/.gradle/caches/transforms-3/812d3057888613e0ba36b827475fe757/transformed/jetified-games-activity-3.0.3/prefab/modules/game-activity_static/libs/android.arm64-v8a/libgame-activity_static.a  -llog   -latomic -lm
  OBJECT_DIR = GameActivity\CMakeFiles\game.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libgame.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = "D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\Debug\3c5q4h5p\obj\arm64-v8a\libgame.so"
  TARGET_PDB = game.so.dbg


#############################################
# Utility command for edit_cache

build GameActivity/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a\GameActivity" && "C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build GameActivity/edit_cache: phony GameActivity/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build GameActivity/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a\GameActivity" && "C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\cpp" -B"D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build GameActivity/rebuild_cache: phony GameActivity/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target swappywrapper


#############################################
# Order-only phony target for swappywrapper

build cmake_object_order_depends_target_swappywrapper: phony || FramePacing/CMakeFiles/swappywrapper.dir

build FramePacing/CMakeFiles/swappywrapper.dir/UnitySwappyWrapper.cpp.o: CXX_COMPILER__swappywrapper_Debug D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/UnitySwappyWrapper.cpp || cmake_object_order_depends_target_swappywrapper
  DEFINES = -DEXTERNAL_FRAME_PACING_CODE -Dswappywrapper_EXPORTS
  DEP_FILE = FramePacing\CMakeFiles\swappywrapper.dir\UnitySwappyWrapper.cpp.o.d
  FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info -fPIC
  INCLUDES = -I"D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing" -isystem C:/Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/include
  OBJECT_DIR = FramePacing\CMakeFiles\swappywrapper.dir
  OBJECT_FILE_DIR = FramePacing\CMakeFiles\swappywrapper.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target swappywrapper


#############################################
# Link the shared library D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\Debug\3c5q4h5p\obj\arm64-v8a\libswappywrapper.so

build D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libswappywrapper.so: CXX_SHARED_LIBRARY_LINKER__swappywrapper_Debug FramePacing/CMakeFiles/swappywrapper.dir/UnitySwappyWrapper.cpp.o | C$:/Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/libs/android.arm64-v8a_API23_NDK23_cpp_shared_Release/libswappy.a
  LANGUAGE_COMPILE_FLAGS = -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -g  -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Qunused-arguments -Wl,--no-undefined -Wl,--wrap=__android_log_print
  LINK_LIBRARIES = -landroid  C:/Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/libs/android.arm64-v8a_API23_NDK23_cpp_shared_Release/libswappy.a  -llog   -latomic -lm
  OBJECT_DIR = FramePacing\CMakeFiles\swappywrapper.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libswappywrapper.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = "D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\Debug\3c5q4h5p\obj\arm64-v8a\libswappywrapper.so"
  TARGET_PDB = swappywrapper.so.dbg


#############################################
# Utility command for edit_cache

build FramePacing/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a\FramePacing" && "C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build FramePacing/edit_cache: phony FramePacing/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build FramePacing/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a\FramePacing" && "C:\Program Files\Unity\Hub\Editor\6000.0.7f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\GitLab\Pryze Unity 6\Pryze Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\cpp" -B"D:\GitLab\Pryze Unity 6\Pryze Unity\.utmp\Debug\3c5q4h5p\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build FramePacing/rebuild_cache: phony FramePacing/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build game: phony D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libgame.so

build libgame.so: phony D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libgame.so

build libswappywrapper.so: phony D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libswappywrapper.so

build swappywrapper: phony D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libswappywrapper.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/GitLab/Pryze Unity 6/Pryze Unity/.utmp/Debug/3c5q4h5p/arm64-v8a

build all: phony GameActivity/all FramePacing/all

# =============================================================================

#############################################
# Folder: D:/GitLab/Pryze Unity 6/Pryze Unity/.utmp/Debug/3c5q4h5p/arm64-v8a/FramePacing

build FramePacing/all: phony D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libswappywrapper.so

# =============================================================================

#############################################
# Folder: D:/GitLab/Pryze Unity 6/Pryze Unity/.utmp/Debug/3c5q4h5p/arm64-v8a/GameActivity

build GameActivity/all: phony D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/Debug/3c5q4h5p/obj/arm64-v8a/libgame.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/adjust_api_level.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/flags.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android-Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/platforms.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Common.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/.utmp/Debug/3c5q4h5p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/game-activity/game-activityConfig.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/.utmp/Debug/3c5q4h5p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/games-frame-pacing/games-frame-pacingConfig.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/.utmp/Debug/3c5q4h5p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/games-frame-pacing/games-frame-pacingConfigVersion.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/adjust_api_level.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/flags.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android-Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/post/Android.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/hooks/pre/Android.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/build/cmake/platforms.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Common.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Program$ Files/Unity/Hub/Editor/6000.0.7f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/.utmp/Debug/3c5q4h5p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/game-activity/game-activityConfig.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/.utmp/Debug/3c5q4h5p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/games-frame-pacing/games-frame-pacingConfig.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/.utmp/Debug/3c5q4h5p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/games-frame-pacing/games-frame-pacingConfigVersion.cmake D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt D$:/GitLab/Pryze$ Unity$ 6/Pryze$ Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
