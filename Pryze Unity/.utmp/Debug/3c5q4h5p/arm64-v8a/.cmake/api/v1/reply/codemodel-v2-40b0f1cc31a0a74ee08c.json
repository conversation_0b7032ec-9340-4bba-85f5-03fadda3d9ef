{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}, {"build": "GameActivity", "jsonFile": "directory-GameActivity-Debug-3edf2ef9e25ca0503118.json", "minimumCMakeVersion": {"string": "3.4.1"}, "parentIndex": 0, "projectIndex": 1, "source": "GameActivity", "targetIndexes": [0]}, {"build": "FramePacing", "jsonFile": "directory-FramePacing-Debug-7f9c8865fd027a154c90.json", "minimumCMakeVersion": {"string": "3.4.1"}, "parentIndex": 0, "projectIndex": 2, "source": "FramePacing", "targetIndexes": [1]}], "name": "Debug", "projects": [{"childIndexes": [1, 2], "directoryIndexes": [0], "name": "Unity"}, {"directoryIndexes": [1], "name": "game", "parentIndex": 0, "targetIndexes": [0]}, {"directoryIndexes": [2], "name": "swappywrapper", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 1, "id": "game::@d02bb112ea9f9c2ed29f", "jsonFile": "target-game-Debug-be96f511ec5c68422a00.json", "name": "game", "projectIndex": 1}, {"directoryIndex": 2, "id": "swappywrapper::@7e25bd1f32ce224db4e9", "jsonFile": "target-swappywrapper-Debug-876988f3ee01e1bc2e1e.json", "name": "swappywrapper", "projectIndex": 2}]}], "kind": "codemodel", "paths": {"build": "D:/GitLab/Pryze Unity 6/Pryze Unity/.utmp/Debug/3c5q4h5p/arm64-v8a", "source": "D:/GitLab/Pryze Unity 6/Pryze Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp"}, "version": {"major": 2, "minor": 3}}